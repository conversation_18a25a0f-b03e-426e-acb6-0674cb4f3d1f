{"cells": [{"cell_type": "markdown", "id": "0c9e547f", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-3/streaming-interruption.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239464-lesson-1-streaming)"]}, {"cell_type": "markdown", "id": "319adfec-2d0a-49f2-87f9-275c4a32add2", "metadata": {}, "source": ["# Streaming\n", "\n", "## Review\n", "\n", "In module 2, covered a few ways to customize graph state and memory.\n", " \n", "We built up to a Chatbot with external memory that can sustain long-running conversations. \n", "\n", "## Goals\n", "\n", "This module will dive into `human-in-the-loop`, which builds on memory and allows users to interact directly with graphs in various ways. \n", "\n", "To set the stage for `human-in-the-loop`, we'll first dive into streaming, which provides several ways to visualize graph output (e.g., node state or chat model tokens) over the course of execution."]}, {"cell_type": "code", "execution_count": null, "id": "db024d1f-feb3-45a0-a55c-e7712a1feefa", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_openai langgraph_sdk"]}, {"cell_type": "markdown", "id": "70d7e41b-c6ba-4e47-b645-6c110bede549", "metadata": {}, "source": ["## Streaming\n", "\n", "LangGraph is built with [first class support for streaming](https://langchain-ai.github.io/langgraph/concepts/low_level/#streaming).\n", "\n", "Let's set up our Chatbot from Module 2, and show various way to stream outputs from the graph during execution. "]}, {"cell_type": "code", "execution_count": 1, "id": "5b430d92-f595-4322-a56e-06de7485daa8", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "4d0682fc", "metadata": {}, "source": ["Note that we use `RunnableConfig` with `call_model` to enable token-wise streaming. This is [only needed with python < 3.11](https://langchain-ai.github.io/langgraph/how-tos/streaming-tokens/). We include in case you are running this notebook in CoLab, which will use python 3.x. "]}, {"cell_type": "code", "execution_count": 1, "id": "2d7321e0-0d99-4efe-a67b-74c12271859b", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.messages import SystemMessage, HumanMessage, RemoveMessage\n", "from langchain_core.runnables import RunnableConfig\n", "\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph import MessagesState\n", "\n", "# LLM\n", "model = ChatOpenAI(model=\"gpt-4o\", temperature=0) \n", "\n", "# State \n", "class State(MessagesState):\n", "    summary: str\n", "\n", "# Define the logic to call the model\n", "def call_model(state: State, config: RunnableConfig):\n", "    \n", "    # Get summary if it exists\n", "    summary = state.get(\"summary\", \"\")\n", "\n", "    # If there is summary, then we add it\n", "    if summary:\n", "        \n", "        # Add summary to system message\n", "        system_message = f\"Summary of conversation earlier: {summary}\"\n", "\n", "        # Append summary to any newer messages\n", "        messages = [SystemMessage(content=system_message)] + state[\"messages\"]\n", "    \n", "    else:\n", "        messages = state[\"messages\"]\n", "    \n", "    response = model.invoke(messages, config)\n", "    return {\"messages\": response}\n", "\n", "def summarize_conversation(state: State):\n", "    \n", "    # First, we get any existing summary\n", "    summary = state.get(\"summary\", \"\")\n", "\n", "    # Create our summarization prompt \n", "    if summary:\n", "        \n", "        # A summary already exists\n", "        summary_message = (\n", "            f\"This is summary of the conversation to date: {summary}\\n\\n\"\n", "            \"Extend the summary by taking into account the new messages above:\"\n", "        )\n", "        \n", "    else:\n", "        summary_message = \"Create a summary of the conversation above:\"\n", "\n", "    # Add prompt to our history\n", "    messages = state[\"messages\"] + [HumanMessage(content=summary_message)]\n", "    response = model.invoke(messages)\n", "    \n", "    # Delete all but the 2 most recent messages\n", "    delete_messages = [RemoveMessage(id=m.id) for m in state[\"messages\"][:-2]]\n", "    return {\"summary\": response.content, \"messages\": delete_messages}\n", "\n", "# Determine whether to end or summarize the conversation\n", "def should_continue(state: State):\n", "    \n", "    \"\"\"Return the next node to execute.\"\"\"\n", "    \n", "    messages = state[\"messages\"]\n", "    \n", "    # If there are more than six messages, then we summarize the conversation\n", "    if len(messages) > 6:\n", "        return \"summarize_conversation\"\n", "    \n", "    # Otherwise we can just end\n", "    return END\n", "\n", "# Define a new graph\n", "workflow = StateGraph(State)\n", "workflow.add_node(\"conversation\", call_model)\n", "workflow.add_node(summarize_conversation)\n", "\n", "# Set the entrypoint as conversation\n", "workflow.add_edge(START, \"conversation\")\n", "workflow.add_conditional_edges(\"conversation\", should_continue)\n", "workflow.add_edge(\"summarize_conversation\", END)\n", "\n", "# Compile\n", "memory = MemorySaver()\n", "graph = workflow.compile(checkpointer=memory)\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"attachments": {}, "cell_type": "markdown", "id": "f847a787-b301-488c-9b58-cba9f389f55d", "metadata": {}, "source": ["### Streaming full state\n", "\n", "Now, let's talk about ways to [stream our graph state](https://langchain-ai.github.io/langgraph/concepts/low_level/#streaming).\n", "\n", "`.stream` and `.astream` are sync and async methods for streaming back results. \n", " \n", "LangGraph supports a few [different streaming modes](https://langchain-ai.github.io/langgraph/how-tos/stream-values/) for [graph state](https://langchain-ai.github.io/langgraph/how-tos/stream-values/):\n", " \n", "* `values`: This streams the full state of the graph after each node is called.\n", "* `updates`: This streams updates to the state of the graph after each node is called.\n", "\n", "![values_vs_updates.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dbaf892d24625a201744e5_streaming1.png)\n", "\n", "Let's look at `stream_mode=\"updates\"`.\n", "\n", "Because we stream with `updates`, we only see updates to the state after node in the graph is run.\n", "\n", "Each `chunk` is a dict with `node_name` as the key and the updated state as the value."]}, {"cell_type": "code", "execution_count": 2, "id": "9a6f8ae9-f244-40c5-a2da-618b72631b22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'conversation': {'messages': AIMessage(content='Hi <PERSON>! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 10, 'prompt_tokens': 11, 'total_tokens': 21}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_25624ae3a5', 'finish_reason': 'stop', 'logprobs': None}, id='run-6d58e31e-a278-4df6-ab0a-bb51d08ca037-0', usage_metadata={'input_tokens': 11, 'output_tokens': 10, 'total_tokens': 21})}}\n"]}], "source": ["# Create a thread\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Start conversation\n", "for chunk in graph.stream({\"messages\": [HumanMessage(content=\"hi! I'm <PERSON>\")]}, config, stream_mode=\"updates\"):\n", "    print(chunk)"]}, {"cell_type": "markdown", "id": "0c4882e9-07dd-4d70-866b-dfc530418cad", "metadata": {}, "source": ["Let's now just print the state update."]}, {"cell_type": "code", "execution_count": 3, "id": "c859c777-cb12-4682-9108-6b367e597b81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON>! How are you doing today?\n"]}], "source": ["# Start conversation\n", "for chunk in graph.stream({\"messages\": [HumanMessage(content=\"hi! I'm <PERSON>\")]}, config, stream_mode=\"updates\"):\n", "    chunk['conversation'][\"messages\"].pretty_print()"]}, {"cell_type": "markdown", "id": "583bf219-6358-4d06-ae99-c40f43569fda", "metadata": {}, "source": ["Now, we can see `stream_mode=\"values\"`.\n", "\n", "This is the `full state` of the graph after the `conversation` node is called."]}, {"cell_type": "code", "execution_count": 4, "id": "6ee763f8-6d1f-491e-8050-fb1439e116df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! I'm <PERSON>\n", "---------------------------------------------------------------------------\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "hi! I'm <PERSON>\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi <PERSON>! How can I assist you today?\n", "---------------------------------------------------------------------------\n"]}], "source": ["# Start conversation, again\n", "config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "\n", "# Start conversation\n", "input_message = HumanMessage(content=\"hi! I'm <PERSON>\")\n", "for event in graph.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    for m in event['messages']:\n", "        m.pretty_print()\n", "    print(\"---\"*25)"]}, {"cell_type": "markdown", "id": "563c198a-d1a4-4700-b7a7-ff5b8e0b25d7", "metadata": {}, "source": ["### Streaming tokens\n", "\n", "We often want to stream more than graph state.\n", "\n", "In particular, with chat model calls it is common to stream the tokens as they are generated.\n", "\n", "We can do this [using the `.astream_events` method](https://langchain-ai.github.io/langgraph/how-tos/streaming-from-final-node/#stream-outputs-from-the-final-node), which streams back events as they happen inside nodes!\n", "\n", "Each event is a dict with a few keys:\n", " \n", "* `event`: This is the type of event that is being emitted. \n", "* `name`: This is the name of event.\n", "* `data`: This is the data associated with the event.\n", "* `metadata`: Contains`langgraph_node`, the node emitting the event.\n", "\n", "Let's have a look."]}, {"cell_type": "code", "execution_count": 5, "id": "6ae8c7a6-c6e7-4cef-ac9f-190d2f4dd763", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/l9/bpjxdmfx7lvd1fbdjn38y5dh0000gn/T/ipykernel_66136/946416104.py:3: Lang<PERSON>hainBetaWarning: This API is in beta and may change in the future.\n", "  async for event in graph.astream_events({\"messages\": [input_message]}, config, version=\"v2\"):\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Node: . Type: on_chain_start. Name: LangGraph\n", "Node: __start__. Type: on_chain_start. Name: __start__\n", "Node: __start__. Type: on_chain_end. Name: __start__\n", "Node: conversation. Type: on_chain_start. Name: conversation\n", "Node: conversation. Type: on_chat_model_start. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_stream. Name: ChatOpenAI\n", "Node: conversation. Type: on_chat_model_end. Name: ChatOpenAI\n", "Node: conversation. Type: on_chain_start. Name: ChannelWrite<conversation,messages,summary>\n", "Node: conversation. Type: on_chain_end. Name: ChannelWrite<conversation,messages,summary>\n", "Node: conversation. Type: on_chain_start. Name: should_continue\n", "Node: conversation. Type: on_chain_end. Name: should_continue\n", "Node: conversation. Type: on_chain_stream. Name: conversation\n", "Node: conversation. Type: on_chain_end. Name: conversation\n", "Node: . Type: on_chain_stream. Name: LangGraph\n", "Node: . Type: on_chain_end. Name: LangGraph\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"3\"}}\n", "input_message = HumanMessage(content=\"Tell me about the 49ers NFL team\")\n", "async for event in graph.astream_events({\"messages\": [input_message]}, config, version=\"v2\"):\n", "    print(f\"Node: {event['metadata'].get('langgraph_node','')}. Type: {event['event']}. Name: {event['name']}\")"]}, {"cell_type": "markdown", "id": "0b63490f-3d24-4f68-95ca-5320ccb61d2d", "metadata": {}, "source": ["The central point is that tokens from chat models within your graph have the `on_chat_model_stream` type.\n", "\n", "We can use `event['metadata']['langgraph_node']` to select the node to stream from.\n", "\n", "And we can use `event['data']` to get the actual data for each event, which in this case is an `AIMessageChunk`. "]}, {"cell_type": "code", "execution_count": 6, "id": "cc3529f8-3960-4d41-9ed6-373f93183950", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chunk': AIMessageChunk(content='', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' San', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Francisco', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' a', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' professional', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' American', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' football', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' based', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' San', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Francisco', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bay', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Area', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' They', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' compete', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' National', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Football', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' League', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' (', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='NFL', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=')', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' as', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' a', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' member', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' league', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=\"'s\", id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' National', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Football', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Conference', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' (', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='N', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='FC', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=')', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' West', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' division', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Here', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' some', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' key', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' points', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' about', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' History', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Founded', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='194', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='6', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Joined', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFL', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='194', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='9', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' as', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' part', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' merger', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' between', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFL', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' All', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-Amer', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ica', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Football', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Conference', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' (', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='AA', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='FC', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=')\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Name', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Origin', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' name', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' \"', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\"', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' is', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' a', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' reference', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' to', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' prospect', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ors', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' who', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' arrived', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Northern', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' California', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' during', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='184', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='9', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Gold', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Rush', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Ach', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ievements', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Super', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bowl', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Championships', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' have', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' won', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' five', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Super', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bowl', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' titles', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' (', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='X', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='VI', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' XIX', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' XX', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='III', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' XX', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='IV', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' XX', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='IX', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=').\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Conference', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Championships', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' They', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' have', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' won', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFC', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Championship', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' seven', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' times', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Division', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Championships', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' has', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' numerous', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFC', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' West', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' division', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' titles', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Not', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='able', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Figures', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Joe', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Montana', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Hall', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Fame', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' quarterback', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' who', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' led', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' to', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' four', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Super', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bowl', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' victories', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Jerry', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Rice', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Wid', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ely', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' considered', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' greatest', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' wide', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' receiver', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFL', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' history', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='<PERSON>', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Young', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Another', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Hall', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Fame', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' quarterback', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' who', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' led', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' to', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' a', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Super', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bowl', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' victory', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Bill', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Walsh', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Legendary', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' head', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' coach', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' known', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' for', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' developing', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' \"', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='West', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Coast', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Off', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ense', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\"\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Home', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Stadium', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Le', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='vi', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=\"'s\", id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Stadium', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Located', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Santa', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Clara', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' California', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' it', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' has', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' been', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=\" team's\", id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' home', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' since', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='201', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='4', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Prior', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' to', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' that', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' they', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' played', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' at', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Cand', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='lestick', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Park', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' San', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Francisco', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Colors', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Masc', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ot', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Colors', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Red', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' gold', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' white', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='-', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' **', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Masc', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ot', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=':**', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' S', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ourd', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ough', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Sam', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Recent', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Performance', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' have', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' experienced', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' various', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' periods', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' success', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' rebuilding', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' They', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' reached', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Super', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Bowl', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='201', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='9', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' season', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' but', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' were', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' defeated', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' by', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Kansas', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' City', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Chiefs', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' team', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' has', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' been', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' competitive', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' recent', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' years', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' often', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' cont', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ending', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' for', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' playoff', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' spots', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Community', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Culture', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' have', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' a', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' strong', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' fan', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' base', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' known', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' for', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' their', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' rich', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' history', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' tradition', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' They', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' also', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' active', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' community', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' service', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' charitable', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' activities', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' through', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Foundation', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='###', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Rival', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ries', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='The', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' have', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' notable', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' rival', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ries', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' with', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' several', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' teams', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' including', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Dallas', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Cowboys', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Seattle', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Seahawks', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Los', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Angeles', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Rams', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' These', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' rival', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ries', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' often', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' marked', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' by', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' intense', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' memorable', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' games', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.\\n\\n', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='Overall', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' San', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' Francisco', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='49', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ers', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' are', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' one', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' most', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' stor', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='ied', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' franchises', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' NFL', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' history', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=',', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' known', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' for', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' their', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' success', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' in', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='198', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='0', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='s', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' ', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='199', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='0', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='s', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' and', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' their', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' continued', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' pursuit', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' of', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' excellence', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' on', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' the', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content=' field', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='.', id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n", "{'chunk': AIMessageChunk(content='', response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_25624ae3a5'}, id='run-b76ec3b8-9c45-42fe-b321-4ec3a69c185c')}\n"]}], "source": ["node_to_stream = 'conversation'\n", "config = {\"configurable\": {\"thread_id\": \"4\"}}\n", "input_message = HumanMessage(content=\"Tell me about the 49ers NFL team\")\n", "async for event in graph.astream_events({\"messages\": [input_message]}, config, version=\"v2\"):\n", "    # Get chat model tokens from a particular node \n", "    if event[\"event\"] == \"on_chat_model_stream\" and event['metadata'].get('langgraph_node','') == node_to_stream:\n", "        print(event[\"data\"])"]}, {"cell_type": "markdown", "id": "226e569a-76c3-43d8-8f89-3ae687efde1c", "metadata": {}, "source": ["As you see above, just use the `chunk` key to get the `AIMessageChunk`."]}, {"cell_type": "code", "execution_count": 7, "id": "3aeae53d-6dcf-40d0-a0c6-c40de492cc83", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|The| San| Francisco| |49|ers| are| a| professional| American| football| team| based| in| the| San| Francisco| Bay| Area|.| They| compete| in| the| National| Football| League| (|NFL|)| as| a| member| of| the| league|'s| National| Football| Conference| (|N|FC|)| West| division|.| Here| are| some| key| points| about| the| team|:\n", "\n", "|###| History|\n", "|-| **|Founded|:**| |194|6| as| a| charter| member| of| the| All|-Amer|ica| Football| Conference| (|AA|FC|)| and| joined| the| NFL| in| |194|9| when| the| leagues| merged|.\n", "|-| **|Team| Name|:**| The| name| \"|49|ers|\"| is| a| reference| to| the| prospect|ors| who| arrived| in| Northern| California| during| the| |184|9| Gold| Rush|.\n", "\n", "|###| Ach|ievements|\n", "|-| **|Super| Bowl| Championships|:**| The| |49|ers| have| won| five| Super| Bowl| titles| (|X|VI|,| XIX|,| XX|III|,| XX|IV|,| and| XX|IX|).\n", "|-| **|Conference| Championships|:**| They| have| won| the| NFC| Championship| seven| times|.\n", "|-| **|Division| Championships|:**| The| team| has| numerous| NFC| West| division| titles|.\n", "\n", "|###| Not|able| Figures|\n", "|-| **|Co|aches|:**| <PERSON>| <PERSON>|,| who| is| credited| with| developing| the| \"|West| Coast| offense|,\"| is| one| of| the| most| famous| coaches| in| |49|ers| history|.\n", "|-| **|Players|:**| The| team| has| had| several| Hall| of| Fame| players|,| including| <PERSON>| Montana|,| <PERSON>| <PERSON>|,| <PERSON>| <PERSON>|,| <PERSON>| L|ott|,| and| many| others|.\n", "\n", "|###| Stadium|\n", "|-| **|Le|vi|'s| Stadium|:**| Located| in| Santa| Clara|,| California|,| it| has| been| the| team's| home| since| |201|4|.| Before| that|,| they| played| at| Cand|lestick| Park| in| San| Francisco|.\n", "\n", "|###| Rival|ries|\n", "|-| **|Dallas| Cowboys|:**| One| of| the| most| stor|ied| rival|ries|,| especially| prominent| during| the| |198|0|s| and| |199|0|s|.\n", "|-| **|Seattle| Seahawks|:**| A| more| recent| but| intense| rivalry|,| particularly| since| the| Seahawks| joined| the| NFC| West| in| |200|2|.\n", "|-| **|Los| Angeles| Rams|:**| A| long|-standing| divis|ional| rivalry|.\n", "\n", "|###| Recent| Performance|\n", "|-| The| |49|ers| have| had| periods| of| both| success| and| struggle| in| recent| years|.| They| reached| the| Super| Bowl| in| the| |201|9| season| but| lost| to| the| Kansas| City| Chiefs|.| The| team| has| been| competitive| in| the| NFC| West| and| continues| to| build| a| strong| roster|.\n", "\n", "|###| Ownership| and| Management|\n", "|-| **|Owner|:**| The| team| is| owned| by| <PERSON>| <PERSON>|<PERSON>|olo| York| and| <PERSON>| <PERSON>|,| with| their| son| <PERSON>| <PERSON>| serving| as| the| CEO|.\n", "|-| **|General| Manager|:**| <PERSON>| <PERSON>|,| a| former| NFL| player| and| <PERSON>| of| F|amer|,| has| been| the| GM| since| |201|7|.\n", "|-| **|Head| Coach|:**| <PERSON>| <PERSON>|ahan|,| known| for| his| offensive| ac|umen|,| has| been| the| head| coach| since| |201|7|.\n", "\n", "|The| |49|ers| are| known| for| their| rich| history|,| iconic| players|,| and| significant| contributions| to| the| game| of| football|.| They| continue| to| be| a| prominent| and| competitive| team| in| the| NFL|.||"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"5\"}}\n", "input_message = HumanMessage(content=\"Tell me about the 49ers NFL team\")\n", "async for event in graph.astream_events({\"messages\": [input_message]}, config, version=\"v2\"):\n", "    # Get chat model tokens from a particular node \n", "    if event[\"event\"] == \"on_chat_model_stream\" and event['metadata'].get('langgraph_node','') == node_to_stream:\n", "        data = event[\"data\"]\n", "        print(data[\"chunk\"].content, end=\"|\")"]}, {"attachments": {}, "cell_type": "markdown", "id": "5826e4d8-846b-4f6c-a5c1-e781d43022db", "metadata": {}, "source": ["### Streaming with LangGraph API\n", "\n", "**⚠️ DISCLAIMER**\n", "\n", "Since the filming of these videos, we've updated Studio so that it can be run locally and opened in your browser. This is now the preferred way to run Studio (rather than using the Desktop App as shown in the video). See documentation [here](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#local-development-server) on the local development server and [here](https://langchain-ai.github.io/langgraph/how-tos/local-studio/#run-the-development-server). To start the local development server, run the following command in your terminal in the `/studio` directory in this module:\n", "\n", "```\n", "langgraph dev\n", "```\n", "\n", "You should see the following output:\n", "```\n", "- 🚀 API: http://127.0.0.1:2024\n", "- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\n", "- 📚 API Docs: http://127.0.0.1:2024/docs\n", "```\n", "\n", "Open your browser and navigate to the Studio UI: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`.\n", "\n", "The LangGraph API [supports editing graph state](https://langchain-ai.github.io/langgraph/cloud/how-tos/human_in_the_loop_edit_state/#initial-invocation). "]}, {"cell_type": "code", "execution_count": null, "id": "8925b632-512b-48e1-9220-61c06bfbf0b8", "metadata": {}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):\n", "    raise Exception(\"Unfortunately LangGraph Studio is currently not supported on Google Colab\")"]}, {"cell_type": "code", "execution_count": 10, "id": "079c2ad6", "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "\n", "# This is the URL of the local development server\n", "URL = \"http://127.0.0.1:2024\"\n", "client = get_client(url=URL)\n", "\n", "# Search all hosted graphs\n", "assistants = await client.assistants.search()"]}, {"cell_type": "markdown", "id": "4d15af9e-0e86-41e3-a5ba-ee2a4aa08a32", "metadata": {}, "source": ["Let's [stream `values`](https://langchain-ai.github.io/langgraph/cloud/how-tos/stream_values/), like before."]}, {"cell_type": "code", "execution_count": 11, "id": "63e3096f-5429-4d3c-8de2-2bddf7266ebf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["StreamPart(event='metadata', data={'run_id': '1ef6a3d0-41eb-66f4-a311-8ebdfa1b281f'})\n", "StreamPart(event='values', data={'messages': [{'content': 'Multiply 2 and 3', 'additional_kwargs': {'example': False, 'additional_kwargs': {}, 'response_metadata': {}}, 'response_metadata': {}, 'type': 'human', 'name': None, 'id': '345c67cf-c958-4f89-b787-540fc025080c', 'example': False}]})\n", "StreamPart(event='values', data={'messages': [{'content': 'Multiply 2 and 3', 'additional_kwargs': {'example': False, 'additional_kwargs': {}, 'response_metadata': {}}, 'response_metadata': {}, 'type': 'human', 'name': None, 'id': '345c67cf-c958-4f89-b787-540fc025080c', 'example': False}, {'content': '', 'additional_kwargs': {'tool_calls': [{'index': 0, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}]}, 'response_metadata': {'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'type': 'ai', 'name': None, 'id': 'run-88179a6d-eb1e-4953-ac42-0b533b6d76f6', 'example': False, 'tool_calls': [{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'type': 'tool_call'}], 'invalid_tool_calls': [], 'usage_metadata': None}]})\n", "StreamPart(event='values', data={'messages': [{'content': 'Multiply 2 and 3', 'additional_kwargs': {'example': False, 'additional_kwargs': {}, 'response_metadata': {}}, 'response_metadata': {}, 'type': 'human', 'name': None, 'id': '345c67cf-c958-4f89-b787-540fc025080c', 'example': False}, {'content': '', 'additional_kwargs': {'tool_calls': [{'index': 0, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}]}, 'response_metadata': {'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'type': 'ai', 'name': None, 'id': 'run-88179a6d-eb1e-4953-ac42-0b533b6d76f6', 'example': False, 'tool_calls': [{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'type': 'tool_call'}], 'invalid_tool_calls': [], 'usage_metadata': None}, {'content': '6', 'additional_kwargs': {}, 'response_metadata': {}, 'type': 'tool', 'name': 'multiply', 'id': '4dd5ce10-ac0b-4a91-b34b-c35109dcbf29', 'tool_call_id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'artifact': None, 'status': 'success'}]})\n", "StreamPart(event='values', data={'messages': [{'content': 'Multiply 2 and 3', 'additional_kwargs': {'example': False, 'additional_kwargs': {}, 'response_metadata': {}}, 'response_metadata': {}, 'type': 'human', 'name': None, 'id': '345c67cf-c958-4f89-b787-540fc025080c', 'example': False}, {'content': '', 'additional_kwargs': {'tool_calls': [{'index': 0, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}]}, 'response_metadata': {'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'type': 'ai', 'name': None, 'id': 'run-88179a6d-eb1e-4953-ac42-0b533b6d76f6', 'example': False, 'tool_calls': [{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'type': 'tool_call'}], 'invalid_tool_calls': [], 'usage_metadata': None}, {'content': '6', 'additional_kwargs': {}, 'response_metadata': {}, 'type': 'tool', 'name': 'multiply', 'id': '4dd5ce10-ac0b-4a91-b34b-c35109dcbf29', 'tool_call_id': 'call_iIPryzZZxRtXozwwhVtFObNO', 'artifact': None, 'status': 'success'}, {'content': 'The result of multiplying 2 and 3 is 6.', 'additional_kwargs': {}, 'response_metadata': {'finish_reason': 'stop', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'type': 'ai', 'name': None, 'id': 'run-b5862486-a25f-48fc-9a03-a8506a6692a8', 'example': False, 'tool_calls': [], 'invalid_tool_calls': [], 'usage_metadata': None}]})\n"]}], "source": ["# Create a new thread\n", "thread = await client.threads.create()\n", "# Input message\n", "input_message = HumanMessage(content=\"Multiply 2 and 3\")\n", "async for event in client.runs.stream(thread[\"thread_id\"], \n", "                                      assistant_id=\"agent\", \n", "                                      input={\"messages\": [input_message]}, \n", "                                      stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "556dc7fd-1cae-404f-816a-f13d772b3b14", "metadata": {}, "source": ["The streamed objects have: \n", "\n", "* `event`: Type\n", "* `data`: State"]}, {"cell_type": "code", "execution_count": 12, "id": "57b735aa-139c-45a3-a850-63519c0004f0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=========================\n", "content='Multiply 2 and 3' additional_kwargs={'additional_kwargs': {'example': False, 'additional_kwargs': {}, 'response_metadata': {}}, 'response_metadata': {}, 'example': False} id='f51807de-6b99-4da4-a798-26cf59d16412'\n", "=========================\n", "content='' additional_kwargs={'additional_kwargs': {'tool_calls': [{'index': 0, 'id': 'call_imZHAw7kvMR2ZeKaQVSlj25C', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}]}, 'response_metadata': {'finish_reason': 'tool_calls', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'example': False, 'invalid_tool_calls': [], 'usage_metadata': None} id='run-fa4ab1c6-274d-4be5-8c4a-a6411c7c35cc' tool_calls=[{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_imZHAw7kvMR2ZeKaQVSlj25C', 'type': 'tool_call'}]\n", "=========================\n", "content='6' additional_kwargs={'additional_kwargs': {}, 'response_metadata': {}, 'status': 'success'} name='multiply' id='3e7bbfb6-aa82-453a-969c-9c753fbd1d74' tool_call_id='call_imZHAw7kvMR2ZeKaQVSlj25C'\n", "=========================\n", "content='The result of multiplying 2 and 3 is 6.' additional_kwargs={'additional_kwargs': {}, 'response_metadata': {'finish_reason': 'stop', 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5'}, 'example': False, 'invalid_tool_calls': [], 'usage_metadata': None} id='run-e8e0d672-cfb2-42be-850a-345df3718f69'\n", "=========================\n"]}], "source": ["from langchain_core.messages import convert_to_messages\n", "thread = await client.threads.create()\n", "input_message = HumanMessage(content=\"Multiply 2 and 3\")\n", "async for event in client.runs.stream(thread[\"thread_id\"], assistant_id=\"agent\", input={\"messages\": [input_message]}, stream_mode=\"values\"):\n", "    messages = event.data.get('messages',None)\n", "    if messages:\n", "        print(convert_to_messages(messages)[-1])\n", "    print('='*25)"]}, {"cell_type": "markdown", "id": "a555d186-27be-4ddf-934c-895a3105035d", "metadata": {}, "source": ["There are some new streaming mode that are only supported via the API.\n", "\n", "For example, we can [use `messages` mode](https://langchain-ai.github.io/langgraph/cloud/how-tos/stream_messages/) to better handle the above case!\n", "\n", "This mode currently assumes that you have a `messages` key in your graph, which is a list of messages.\n", "\n", "All events emitted using `messages` mode have two attributes:\n", "\n", "* `event`: This is the name of the event\n", "* `data`: This is data associated with the event"]}, {"cell_type": "code", "execution_count": 13, "id": "4abd91f6-63c0-41ee-9988-7c8248b88a45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["metadata\n", "messages/complete\n", "messages/metadata\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/complete\n", "messages/complete\n", "messages/metadata\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/partial\n", "messages/complete\n"]}], "source": ["thread = await client.threads.create()\n", "input_message = HumanMessage(content=\"Multiply 2 and 3\")\n", "async for event in client.runs.stream(thread[\"thread_id\"], \n", "                                      assistant_id=\"agent\", \n", "                                      input={\"messages\": [input_message]}, \n", "                                      stream_mode=\"messages\"):\n", "    print(event.event)"]}, {"cell_type": "markdown", "id": "8de2f1ea-b232-43fc-af7a-320efce83381", "metadata": {}, "source": ["We can see a few events: \n", "\n", "* `metadata`: metadata about the run\n", "* `messages/complete`: fully formed message \n", "* `messages/partial`: chat model tokens\n", "\n", "You can dig further into the types [here](https://langchain-ai.github.io/langgraph/cloud/concepts/api/#modemessages).\n", "\n", "Now, let's show how to stream these messages. \n", "\n", "We'll define a helper function for better formatting of the tool calls in messages."]}, {"cell_type": "code", "execution_count": 14, "id": "50a85e16-6e3f-4f14-bcf9-8889a762f522", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata: Run ID - 1ef6a3da-687f-6253-915a-701de5327165\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2, 'b': 3}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2, 'b': 3}\n", "--------------------------------------------------\n", "Tool Calls:\n", "Tool Call ID: call_IL4MGMtr1fEpR3Yd9c2goLd8, Function: multiply, Arguments: {'a': 2, 'b': 3}\n", "Response Metadata: Finish Reason - tool_calls\n", "--------------------------------------------------\n", "--------------------------------------------------\n", "AI: The\n", "--------------------------------------------------\n", "AI: The result\n", "--------------------------------------------------\n", "AI: The result of\n", "--------------------------------------------------\n", "AI: The result of multiplying\n", "--------------------------------------------------\n", "AI: The result of multiplying \n", "--------------------------------------------------\n", "AI: The result of multiplying 2\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and \n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3 is\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3 is \n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3 is 6\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3 is 6.\n", "--------------------------------------------------\n", "AI: The result of multiplying 2 and 3 is 6.\n", "Response Metadata: Finish Reason - stop\n", "--------------------------------------------------\n"]}], "source": ["thread = await client.threads.create()\n", "input_message = HumanMessage(content=\"Multiply 2 and 3\")\n", "\n", "def format_tool_calls(tool_calls):\n", "    \"\"\"\n", "    Format a list of tool calls into a readable string.\n", "\n", "    Args:\n", "        tool_calls (list): A list of dictionaries, each representing a tool call.\n", "            Each dictionary should have 'id', 'name', and 'args' keys.\n", "\n", "    Returns:\n", "        str: A formatted string of tool calls, or \"No tool calls\" if the list is empty.\n", "\n", "    \"\"\"\n", "\n", "    if tool_calls:\n", "        formatted_calls = []\n", "        for call in tool_calls:\n", "            formatted_calls.append(\n", "                f\"Tool Call ID: {call['id']}, Function: {call['name']}, Arguments: {call['args']}\"\n", "            )\n", "        return \"\\n\".join(formatted_calls)\n", "    return \"No tool calls\"\n", "\n", "async for event in client.runs.stream(\n", "    thread[\"thread_id\"],\n", "    assistant_id=\"agent\",\n", "    input={\"messages\": [input_message]},\n", "    stream_mode=\"messages\",):\n", "    \n", "    # Handle metadata events\n", "    if event.event == \"metadata\":\n", "        print(f\"Metadata: Run ID - {event.data['run_id']}\")\n", "        print(\"-\" * 50)\n", "    \n", "    # Handle partial message events\n", "    elif event.event == \"messages/partial\":\n", "        for data_item in event.data:\n", "            # Process user messages\n", "            if \"role\" in data_item and data_item[\"role\"] == \"user\":\n", "                print(f\"Human: {data_item['content']}\")\n", "            else:\n", "                # Extract relevant data from the event\n", "                tool_calls = data_item.get(\"tool_calls\", [])\n", "                invalid_tool_calls = data_item.get(\"invalid_tool_calls\", [])\n", "                content = data_item.get(\"content\", \"\")\n", "                response_metadata = data_item.get(\"response_metadata\", {})\n", "\n", "                if content:\n", "                    print(f\"AI: {content}\")\n", "\n", "                if tool_calls:\n", "                    print(\"Tool Calls:\")\n", "                    print(format_tool_calls(tool_calls))\n", "\n", "                if invalid_tool_calls:\n", "                    print(\"Invalid Tool Calls:\")\n", "                    print(format_tool_calls(invalid_tool_calls))\n", "\n", "                if response_metadata:\n", "                    finish_reason = response_metadata.get(\"finish_reason\", \"N/A\")\n", "                    print(f\"Response Metadata: Finish Reason - {finish_reason}\")\n", "                    \n", "        print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "1ae885f8-102f-448a-9d68-8ded8d2bbd18", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}