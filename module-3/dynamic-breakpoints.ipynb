{"cells": [{"cell_type": "markdown", "id": "2109e6a2", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-3/dynamic-breakpoints.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239526-lesson-4-dynamic-breakpoints)"]}, {"cell_type": "markdown", "id": "d0cefea1-f982-4bb1-b691-27a855bfdccb", "metadata": {}, "source": ["# Dynamic breakpoints \n", "\n", "## Review\n", "\n", "We discussed motivations for human-in-the-loop:\n", "\n", "(1) `Approval` - We can interrupt our agent, surface state to a user, and allow the user to accept an action\n", "\n", "(2) `Debugging` - We can rewind the graph to reproduce or avoid issues\n", "\n", "(3) `Editing` - You can modify the state \n", "\n", "We covered breakpoints as a general way to stop the graph at specific steps, which enables use-cases like `Approval`\n", "\n", "We also showed how to edit graph state, and introduce human feedback. \n", "\n", "## Goals\n", "\n", "Breakpoints are set by the developer on a specific node during graph compilation. \n", "\n", "But, sometimes it is helpful to allow the graph **dynamically interrupt** itself!\n", "\n", "This is an internal breakpoint, and [can be achieved using `NodeInterrupt`](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/dynamic_breakpoints/#run-the-graph-with-dynamic-interrupt).\n", "\n", "This has a few specific benefits: \n", "\n", "(1) you can do it conditionally (from inside a node based on developer-defined logic).\n", "\n", "(2) you can communicate to the user why its interrupted (by passing whatever you want to the `NodeInterrupt`).\n", "\n", "Let's create a graph where a `NodeInterrupt` is thrown based upon length of the input."]}, {"cell_type": "code", "execution_count": null, "id": "387d8d87-598a-485a-a99f-a9270a7c2e73", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_openai langgraph_sdk"]}, {"cell_type": "code", "execution_count": 1, "id": "6248f166-2013-445a-b4ae-1fb7b92f8c32", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "from typing_extensions import TypedDict\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.errors import NodeInterrupt\n", "from langgraph.graph import START, END, StateGraph\n", "\n", "class State(TypedDict):\n", "    input: str\n", "\n", "def step_1(state: State) -> State:\n", "    print(\"---Step 1---\")\n", "    return state\n", "\n", "def step_2(state: State) -> State:\n", "    # Let's optionally raise a NodeInterrupt if the length of the input is longer than 5 characters\n", "    if len(state['input']) > 5:\n", "        raise NodeInterrupt(f\"Received input that is longer than 5 characters: {state['input']}\")\n", "    \n", "    print(\"---Step 2---\")\n", "    return state\n", "\n", "def step_3(state: State) -> State:\n", "    print(\"---Step 3---\")\n", "    return state\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"step_2\", step_2)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"step_2\")\n", "builder.add_edge(\"step_2\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Compile the graph with memory\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "b2c6e5c8-0556-43d1-9eef-b3af32728f74", "metadata": {}, "source": ["Let's run the graph with an input that's longer than 5 characters. "]}, {"cell_type": "code", "execution_count": 2, "id": "de73c9ce-ccc5-4ffd-8d82-7018364e7c4f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hello world'}\n", "---Step 1---\n", "{'input': 'hello world'}\n"]}], "source": ["initial_input = {\"input\": \"hello world\"}\n", "thread_config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "id": "da79063f-5b67-49dd-8ef0-3eae4c480cb5", "metadata": {}, "source": ["If we inspect the graph state at this point, we the node set to execute next (`step_2`).\n"]}, {"cell_type": "code", "execution_count": 3, "id": "34706f0d-379b-4236-a42e-c8e52b27fb22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('step_2',)\n"]}], "source": ["state = graph.get_state(thread_config)\n", "print(state.next)"]}, {"cell_type": "markdown", "id": "3ed78755-f1e8-4c66-a4f8-a7ccff472c91", "metadata": {}, "source": ["We can see that the `Interrupt` is logged to state."]}, {"cell_type": "code", "execution_count": 4, "id": "93815a05-819a-4050-8834-73236fa910dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(PregelTask(id='6eb3910d-e231-5ba2-b25e-28ad575690bd', name='step_2', error=None, interrupts=(Interrupt(value='Received input that is longer than 5 characters: hello world', when='during'),), state=None),)\n"]}], "source": ["print(state.tasks)"]}, {"cell_type": "markdown", "id": "27d74573-b62c-4ac1-a142-d04c2dccfd08", "metadata": {}, "source": ["We can try to resume the graph from the breakpoint. \n", "\n", "But, this just re-runs the same node! \n", "\n", "Unless state is changed we will be stuck here."]}, {"cell_type": "code", "execution_count": 5, "id": "b735875e-62c6-4253-ba85-7ccf93a353b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hello world'}\n"]}], "source": ["for event in graph.stream(None, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "code", "execution_count": 6, "id": "1e3bc5e3-7a2f-49a1-8bdc-fd3597bd5fae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('step_2',)\n"]}], "source": ["state = graph.get_state(thread_config)\n", "print(state.next)"]}, {"cell_type": "markdown", "id": "79ab61de-5c3f-44a5-b417-e36b1a2f26dd", "metadata": {}, "source": ["Now, we can update state."]}, {"cell_type": "code", "execution_count": 7, "id": "6f08dff4-3399-46de-a9ba-ba89b8cdb61e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'configurable': {'thread_id': '1',\n", "  'checkpoint_ns': '',\n", "  'checkpoint_id': '1ef6a434-06cf-6f1e-8002-0ea6dc69e075'}}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.update_state(\n", "    thread_config,\n", "    {\"input\": \"hi\"},\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "4cb3f62b-fccd-47c3-af1e-541969e4d804", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': 'hi'}\n", "---Step 2---\n", "{'input': 'hi'}\n", "---Step 3---\n", "{'input': 'hi'}\n"]}], "source": ["for event in graph.stream(None, thread_config, stream_mode=\"values\"):\n", "    print(event)"]}, {"attachments": {}, "cell_type": "markdown", "id": "76e3dea8-8270-42c7-8d24-606b79b9c6aa", "metadata": {}, "source": ["### Usage with LangGraph API\n", "\n", "**⚠️ DISCLAIMER**\n", "\n", "Since the filming of these videos, we've updated Studio so that it can be run locally and opened in your browser. This is now the preferred way to run Studio (rather than using the Desktop App as shown in the video). See documentation [here](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#local-development-server) on the local development server and [here](https://langchain-ai.github.io/langgraph/how-tos/local-studio/#run-the-development-server). To start the local development server, run the following command in your terminal in the `/studio` directory in this module:\n", "\n", "```\n", "langgraph dev\n", "```\n", "\n", "You should see the following output:\n", "```\n", "- 🚀 API: http://127.0.0.1:2024\n", "- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\n", "- 📚 API Docs: http://127.0.0.1:2024/docs\n", "```\n", "\n", "Open your browser and navigate to the Studio UI: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`."]}, {"cell_type": "code", "execution_count": 1, "id": "be02c417-5adc-4789-aa90-02fd2312eb53", "metadata": {}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):\n", "    raise Exception(\"Unfortunately LangGraph Studio is currently not supported on Google Colab\")"]}, {"cell_type": "markdown", "id": "2390ff2e-6b1a-4c6e-b0ce-debd45085dc8", "metadata": {}, "source": ["We connect to it via the SDK."]}, {"cell_type": "code", "execution_count": 2, "id": "4696327d", "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "\n", "# This is the URL of the local development server\n", "URL = \"http://127.0.0.1:2024\"\n", "client = get_client(url=URL)\n", "\n", "# Search all hosted graphs\n", "assistants = await client.assistants.search()"]}, {"cell_type": "code", "execution_count": 12, "id": "8cb892cb-c79c-46bb-820b-d0479e71c5c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Receiving new event of type: metadata...\n", "{'run_id': '1ef6a43a-1b04-64d0-9a79-1caff72c8a89'}\n", "\n", "\n", "\n", "Receiving new event of type: values...\n", "{'input': 'hello world'}\n", "\n", "\n", "\n", "Receiving new event of type: values...\n", "{'input': 'hello world'}\n", "\n", "\n", "\n"]}], "source": ["thread = await client.threads.create()\n", "input_dict = {\"input\": \"hello world\"}\n", "\n", "async for chunk in client.runs.stream(\n", "    thread[\"thread_id\"],\n", "    assistant_id=\"dynamic_breakpoints\",\n", "    input=input_dict,\n", "    stream_mode=\"values\",):\n", "    \n", "    print(f\"Receiving new event of type: {chunk.event}...\")\n", "    print(chunk.data)\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 13, "id": "0ba7d9da", "metadata": {}, "outputs": [], "source": ["current_state = await client.threads.get_state(thread['thread_id'])"]}, {"cell_type": "code", "execution_count": 14, "id": "9610fc2b-ae39-4ffa-84af-b049e7d22cd6", "metadata": {}, "outputs": [{"data": {"text/plain": ["['step_2']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["current_state['next']"]}, {"cell_type": "code", "execution_count": 15, "id": "1e880cf0-18b1-4f7b-a770-24d45dd22757", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'configurable': {'thread_id': 'ea8c2912-987e-49d9-b890-6e81d46065f9',\n", "  'checkpoint_ns': '',\n", "  'checkpoint_id': '1ef6a43a-64b2-6e85-8002-3cf4f2873968'},\n", " 'checkpoint_id': '1ef6a43a-64b2-6e85-8002-3cf4f2873968'}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["await client.threads.update_state(thread['thread_id'], {\"input\": \"hi!\"})"]}, {"cell_type": "code", "execution_count": 23, "id": "16dc65b9-95c0-46eb-9f73-da0a35e70034", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Receiving new event of type: metadata...\n", "{'run_id': '1ef64c33-fb34-6eaf-8b59-1d85c5b8acc9'}\n", "\n", "\n", "\n", "Receiving new event of type: values...\n", "{'input': 'hi!'}\n", "\n", "\n", "\n", "Receiving new event of type: values...\n", "{'input': 'hi!'}\n", "\n", "\n", "\n"]}], "source": ["async for chunk in client.runs.stream(\n", "    thread[\"thread_id\"],\n", "    assistant_id=\"dynamic_breakpoints\",\n", "    input=None,\n", "    stream_mode=\"values\",):\n", "    \n", "    print(f\"Receiving new event of type: {chunk.event}...\")\n", "    print(chunk.data)\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 16, "id": "5f662b10-ad4c-45c7-a420-ded8ccae8faa", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'values': {'input': 'hi!'},\n", " 'next': ['step_2'],\n", " 'tasks': [{'id': '858e41b2-6501-585c-9bca-55c1e729ef91',\n", "   'name': 'step_2',\n", "   'error': None,\n", "   'interrupts': [],\n", "   'state': None}],\n", " 'metadata': {'step': 2,\n", "  'source': 'update',\n", "  'writes': {'step_1': {'input': 'hi!'}},\n", "  'parents': {},\n", "  'graph_id': 'dynamic_breakpoints'},\n", " 'created_at': '2024-09-03T22:27:05.707260+00:00',\n", " 'checkpoint_id': '1ef6a43a-64b2-6e85-8002-3cf4f2873968',\n", " 'parent_checkpoint_id': '1ef6a43a-1cb8-6c3d-8001-7b11d0d34f00'}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["current_state = await client.threads.get_state(thread['thread_id'])\n", "current_state"]}, {"cell_type": "code", "execution_count": null, "id": "873b3696-df61-4f2e-94d8-089b7072aafa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "academy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}