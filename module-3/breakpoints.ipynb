{"cells": [{"cell_type": "markdown", "id": "1012a788", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-3/breakpoints.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239469-lesson-2-breakpoints)"]}, {"cell_type": "markdown", "id": "d4aa16f5-abc8-4ed3-8a71-54837fe46917", "metadata": {}, "source": ["# Breakpoints\n", "\n", "## Review\n", "\n", "For `human-in-the-loop`, we often want to see our graph outputs as its running. \n", "\n", "We laid the foundations for this with streaming. \n", "\n", "## Goals\n", "\n", "Now, let's talk about the motivations for `human-in-the-loop`:\n", "\n", "(1) `Approval` - We can interrupt our agent, surface state to a user, and allow the user to accept an action\n", "\n", "(2) `Debugging` - We can rewind the graph to reproduce or avoid issues\n", "\n", "(3) `Editing` - You can modify the state \n", "\n", "LangGraph offers several ways to get or update agent state to support various `human-in-the-loop` workflows.\n", "\n", "First, we'll introduce [breakpoints](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/breakpoints/#simple-usage), which provide a simple way to stop the graph at specific steps. \n", "\n", "We'll show how this enables user `approval`."]}, {"cell_type": "code", "execution_count": null, "id": "35842345-0694-4f0a-aa62-7d4898abf653", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_openai langgraph_sdk langgraph-prebuilt"]}, {"cell_type": "code", "execution_count": null, "id": "67d91f7c", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "31d8b4cd-e3ff-48cc-b7b2-f83fadb1c86b", "metadata": {}, "source": ["## Breakpoints for human approval\n", "\n", "Let's re-consider the simple agent that we worked with in Module 1. \n", "\n", "Let's assume that are concerned about tool use: we want to approve the agent to use any of its tools.\n", " \n", "All we need to do is simply compile the graph with `interrupt_before=[\"tools\"]` where `tools` is our tools node.\n", "\n", "This means that the execution will be interrupted before the node `tools`, which executes the tool call."]}, {"cell_type": "code", "execution_count": 4, "id": "b94d1a90-2fe3-4b2a-a901-3bdb89e37edc", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a * b\n", "\n", "# This will be a tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Adds a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a + b\n", "\n", "def divide(a: int, b: int) -> float:\n", "    \"\"\"Divide a by b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a / b\n", "\n", "tools = [add, multiply, divide]\n", "llm = ChatOpenAI(model=\"gpt-4o\")\n", "llm_with_tools = llm.bind_tools(tools)"]}, {"cell_type": "code", "execution_count": 5, "id": "ac06feae-d12b-490b-95e7-38cf40b74202", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import MessagesState\n", "from langgraph.graph import START, StateGraph\n", "from langgraph.prebuilt import tools_condition, ToolNode\n", "\n", "from langchain_core.messages import AIMessage, HumanMessage, SystemMessage\n", "\n", "# System message\n", "sys_msg = SystemMessage(content=\"You are a helpful assistant tasked with performing arithmetic on a set of inputs.\")\n", "\n", "# Node\n", "def assistant(state: MessagesState):\n", "   return {\"messages\": [llm_with_tools.invoke([sys_msg] + state[\"messages\"])]}\n", "\n", "# Graph\n", "builder = StateGraph(MessagesState)\n", "\n", "# Define nodes: these do the work\n", "builder.add_node(\"assistant\", assistant)\n", "builder.add_node(\"tools\", ToolNode(tools))\n", "\n", "# Define edges: these determine the control flow\n", "builder.add_edge(START, \"assistant\")\n", "builder.add_conditional_edges(\n", "    \"assistant\",\n", "    # If the latest message (result) from assistant is a tool call -> tools_condition routes to tools\n", "    # If the latest message (result) from assistant is a not a tool call -> tools_condition routes to END\n", "    tools_condition,\n", ")\n", "builder.add_edge(\"tools\", \"assistant\")\n", "\n", "memory = MemorySaver()\n", "graph = builder.compile(interrupt_before=[\"tools\"], checkpointer=memory)\n", "\n", "# Show\n", "display(Image(graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 6, "id": "a783efac-46a9-4fb4-a1c6-a11b02540448", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Multiply 2 and 3\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (call_oFkGpnO8CuwW9A1rk49nqBpY)\n", " Call ID: call_oFkGpnO8CuwW9A1rk49nqBpY\n", "  Args:\n", "    a: 2\n", "    b: 3\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": HumanMessage(content=\"Multiply 2 and 3\")}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"values\"):\n", "    event['messages'][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "50d49669-b1a5-42c2-bdb8-052da89bd7c4", "metadata": {}, "source": ["We can get the state and look at the next node to call.\n", "\n", "This is a nice way to see that the graph has been interrupted."]}, {"cell_type": "code", "execution_count": 7, "id": "61569596-8342-4a37-9c99-e3a9dccb18ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["('tools',)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["state = graph.get_state(thread)\n", "state.next"]}, {"attachments": {}, "cell_type": "markdown", "id": "2fea0fb5-3145-4f34-bcc0-9c9e8972d6b4", "metadata": {}, "source": ["Now, we'll introduce a nice trick.\n", "\n", "When we invoke the graph with `None`, it will just continue from the last state checkpoint!\n", "\n", "![breakpoints.jpg](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dbae7985b747dfed67775d_breakpoints1.png)\n", "\n", "For clarity, LangGraph will re-emit the current state, which contains the `AIMessage` with tool call.\n", "\n", "And then it will proceed to execute the following steps in the graph, which start with the tool node.\n", "\n", "We see that the tool node is run with this tool call, and it's passed back to the chat model for our final answer."]}, {"cell_type": "code", "execution_count": 8, "id": "896a5f41-7386-4bfa-a78e-3e6ca5e26641", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (call_oFkGpnO8CuwW9A1rk49nqBpY)\n", " Call ID: call_oFkGpnO8CuwW9A1rk49nqBpY\n", "  Args:\n", "    a: 2\n", "    b: 3\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "6\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The result of multiplying 2 and 3 is 6.\n"]}], "source": ["for event in graph.stream(None, thread, stream_mode=\"values\"):\n", "    event['messages'][-1].pretty_print()"]}, {"cell_type": "markdown", "id": "37f91a0c-7cc1-4437-adc7-b36abb29beb1", "metadata": {}, "source": ["Now, lets bring these together with a specific user approval step that accepts user input."]}, {"cell_type": "code", "execution_count": 9, "id": "95a0eb50-66e3-4538-8103-207aae175154", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Multiply 2 and 3\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (call_tpHvTmsHSjSpYnymzdx553SU)\n", " Call ID: call_tpHvTmsHSjSpYnymzdx553SU\n", "  Args:\n", "    a: 2\n", "    b: 3\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (call_tpHvTmsHSjSpYnymzdx553SU)\n", " Call ID: call_tpHvTmsHSjSpYnymzdx553SU\n", "  Args:\n", "    a: 2\n", "    b: 3\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: multiply\n", "\n", "6\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The result of multiplying 2 and 3 is 6.\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": HumanMessage(content=\"Multiply 2 and 3\")}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"2\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"values\"):\n", "    event['messages'][-1].pretty_print()\n", "\n", "# Get user feedback\n", "user_approval = input(\"Do you want to call the tool? (yes/no): \")\n", "\n", "# Check approval\n", "if user_approval.lower() == \"yes\":\n", "    \n", "    # If approved, continue the graph execution\n", "    for event in graph.stream(None, thread, stream_mode=\"values\"):\n", "        event['messages'][-1].pretty_print()\n", "        \n", "else:\n", "    print(\"Operation cancelled by user.\")"]}, {"attachments": {}, "cell_type": "markdown", "id": "b8ff8762-6fa1-4373-954a-e7f479ee0efb", "metadata": {}, "source": ["### Breakpoints with LangGraph API\n", "\n", "**⚠️ DISCLAIMER**\n", "\n", "Since the filming of these videos, we've updated Studio so that it can be run locally and opened in your browser. This is now the preferred way to run Studio (rather than using the Desktop App as shown in the video). See documentation [here](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#local-development-server) on the local development server and [here](https://langchain-ai.github.io/langgraph/how-tos/local-studio/#run-the-development-server). To start the local development server, run the following command in your terminal in the `/studio` directory in this module:\n", "\n", "```\n", "langgraph dev\n", "```\n", "\n", "You should see the following output:\n", "```\n", "- 🚀 API: http://127.0.0.1:2024\n", "- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\n", "- 📚 API Docs: http://127.0.0.1:2024/docs\n", "```\n", "\n", "Open your browser and navigate to the Studio UI: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`.\n", "\n", "The LangGraph API [supports breakpoints](https://langchain-ai.github.io/langgraph/cloud/how-tos/human_in_the_loop_breakpoint/#sdk-initialization). "]}, {"cell_type": "code", "execution_count": 1, "id": "63c2eaf1-6b8b-4d80-9902-98ae5587bcf9", "metadata": {}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):\n", "    raise Exception(\"Unfortunately LangGraph Studio is currently not supported on Google Colab\")"]}, {"cell_type": "code", "execution_count": 2, "id": "fb1dd890-c216-4802-9e33-b637e491e144", "metadata": {}, "outputs": [], "source": ["# This is the URL of the local development server\n", "from langgraph_sdk import get_client\n", "client = get_client(url=\"http://127.0.0.1:2024\")"]}, {"cell_type": "markdown", "id": "1e80d969-d065-45d7-8bfc-a403a0a1079b", "metadata": {}, "source": ["As shown above, we can add `interrupt_before=[\"node\"]` when compiling the graph that is running in Studio.\n", "\n", "However, with the API, you can also pass `interrupt_before` to the stream method directly. "]}, {"cell_type": "code", "execution_count": 10, "id": "de9c5017-3a15-46f6-8edf-3997613da323", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Receiving new event of type: metadata...\n", "--------------------------------------------------\n", "Receiving new event of type: values...\n", "{'content': 'Multiply 2 and 3', 'additional_kwargs': {}, 'response_metadata': {}, 'type': 'human', 'name': None, 'id': '2a3b1e7a-f6d9-44c2-a4b4-b7f67aa3691c', 'example': False}\n", "--------------------------------------------------\n", "Receiving new event of type: values...\n", "{'content': '', 'additional_kwargs': {'tool_calls': [{'id': 'call_ElnkVOf1H80dlwZLqO0PQTwS', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}], 'refusal': None}, 'response_metadata': {'token_usage': {'completion_tokens': 18, 'prompt_tokens': 134, 'total_tokens': 152, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_eb9dce56a8', 'finish_reason': 'tool_calls', 'logprobs': None}, 'type': 'ai', 'name': None, 'id': 'run-89ee14dc-5f46-4dd9-91d9-e922c4a23572-0', 'example': False, 'tool_calls': [{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_ElnkVOf1H80dlwZLqO0PQTwS', 'type': 'tool_call'}], 'invalid_tool_calls': [], 'usage_metadata': {'input_tokens': 134, 'output_tokens': 18, 'total_tokens': 152, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}}\n", "--------------------------------------------------\n"]}], "source": ["initial_input = {\"messages\": HumanMessage(content=\"Multiply 2 and 3\")}\n", "thread = await client.threads.create()\n", "async for chunk in client.runs.stream(\n", "    thread[\"thread_id\"],\n", "    assistant_id=\"agent\",\n", "    input=initial_input,\n", "    stream_mode=\"values\",\n", "    interrupt_before=[\"tools\"],\n", "):\n", "    print(f\"Receiving new event of type: {chunk.event}...\")\n", "    messages = chunk.data.get('messages', [])\n", "    if messages:\n", "        print(messages[-1])\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "id": "b64272d1-c6ee-435f-9890-9b6c3525ca6c", "metadata": {}, "source": ["Now, we can proceed from the breakpoint just like we did before by passing the `thread_id` and `None` as the input!"]}, {"cell_type": "code", "execution_count": 11, "id": "76284730-9c90-46c4-8295-400a49760b07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Receiving new event of type: metadata...\n", "--------------------------------------------------\n", "Receiving new event of type: values...\n", "{'content': '', 'additional_kwargs': {'tool_calls': [{'id': 'call_ElnkVOf1H80dlwZLqO0PQTwS', 'function': {'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}, 'type': 'function'}], 'refusal': None}, 'response_metadata': {'token_usage': {'completion_tokens': 18, 'prompt_tokens': 134, 'total_tokens': 152, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_eb9dce56a8', 'finish_reason': 'tool_calls', 'logprobs': None}, 'type': 'ai', 'name': None, 'id': 'run-89ee14dc-5f46-4dd9-91d9-e922c4a23572-0', 'example': False, 'tool_calls': [{'name': 'multiply', 'args': {'a': 2, 'b': 3}, 'id': 'call_ElnkVOf1H80dlwZLqO0PQTwS', 'type': 'tool_call'}], 'invalid_tool_calls': [], 'usage_metadata': {'input_tokens': 134, 'output_tokens': 18, 'total_tokens': 152, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}}\n", "--------------------------------------------------\n", "Receiving new event of type: values...\n", "{'content': '6', 'additional_kwargs': {}, 'response_metadata': {}, 'type': 'tool', 'name': 'multiply', 'id': '5331919f-a26b-4d75-bf33-6dfaea2be1f7', 'tool_call_id': 'call_ElnkVOf1H80dlwZLqO0PQTwS', 'artifact': None, 'status': 'success'}\n", "--------------------------------------------------\n", "Receiving new event of type: values...\n", "{'content': 'The result of multiplying 2 and 3 is 6.', 'additional_kwargs': {'refusal': None}, 'response_metadata': {'token_usage': {'completion_tokens': 15, 'prompt_tokens': 159, 'total_tokens': 174, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_eb9dce56a8', 'finish_reason': 'stop', 'logprobs': None}, 'type': 'ai', 'name': None, 'id': 'run-06b901ad-0760-4986-9d3f-a566e0d52efd-0', 'example': False, 'tool_calls': [], 'invalid_tool_calls': [], 'usage_metadata': {'input_tokens': 159, 'output_tokens': 15, 'total_tokens': 174, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}}\n", "--------------------------------------------------\n"]}], "source": ["async for chunk in client.runs.stream(\n", "    thread[\"thread_id\"],\n", "    \"agent\",\n", "    input=None,\n", "    stream_mode=\"values\",\n", "    interrupt_before=[\"tools\"],\n", "):\n", "    print(f\"Receiving new event of type: {chunk.event}...\")\n", "    messages = chunk.data.get('messages', [])\n", "    if messages:\n", "        print(messages[-1])\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "id": "4575970f-42e2-4d03-b18a-aacaa8233b53", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "academy", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}