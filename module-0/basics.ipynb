{"cells": [{"cell_type": "markdown", "id": "660ce795-9307-4c2c-98a1-beabcb36c740", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-0/basics.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/56295530-getting-set-up-video-guide)"]}, {"cell_type": "markdown", "id": "ef597741-3211-4ecc-92f7-f58023ee237e", "metadata": {}, "source": ["# LangChain Academy\n", "\n", "Welcome to LangChain Academy! \n", "\n", "## Context\n", "\n", "At LangChain, we aim to make it easy to build LLM applications. One type of LLM application you can build is an agent. There’s a lot of excitement around building agents because they can automate a wide range of tasks that were previously impossible. \n", "\n", "In practice though, it is incredibly difficult to build systems that reliably execute on these tasks. As we’ve worked with our users to put agents into production, we’ve learned that more control is often necessary. You might need an agent to always call a specific tool first or use different prompts based on its state. \n", "\n", "To tackle this problem, we’ve built [LangGraph](https://langchain-ai.github.io/langgraph/) — a framework for building agent and multi-agent applications. Separate from the LangChain package, LangGraph’s core design philosophy is to help developers add better precision and control into agent workflows, suitable for the complexity of real-world systems.\n", "\n", "## Course Structure\n", "\n", "The course is structured as a set of modules, with each module focused on a particular theme related to LangGraph. You will see a folder for each module, which contains a series of notebooks. A video will accompany each notebook to help walk through the concepts, but the notebooks are also stand-alone, meaning that they contain explanations and can be viewed independently of the videos. Each module folder also contains a `studio` folder, which contains a set of graphs that can be loaded into [LangGraph Studio](https://github.com/langchain-ai/langgraph-studio), our IDE for building LangGraph applications.\n", "\n", "## Setup\n", "\n", "Before you begin, please follow the instructions in the `README` to create an environment and install dependencies.\n", "\n", "## Chat models\n", "\n", "In this course, we'll be using [Chat Models](https://python.langchain.com/v0.2/docs/concepts/#chat-models), which do a few things take a sequence of messages as inputs and return chat messages as outputs. LangChain does not host any Chat Models, rather we rely on third party integrations. [Here](https://python.langchain.com/v0.2/docs/integrations/chat/) is a list of 3rd party chat model integrations within LangChain! By default, the course will use [ChatOpenAI](https://python.langchain.com/v0.2/docs/integrations/chat/openai/) because it is both popular and performant. As noted, please ensure that you have an `OPENAI_API_KEY`.\n", "\n", "Let's check that your `OPENAI_API_KEY` is set and, if not, you will be asked to enter it."]}, {"cell_type": "code", "execution_count": null, "id": "0f9a52c8", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langchain_openai langchain_core langchain_community tavily-python"]}, {"cell_type": "code", "execution_count": 1, "id": "c2a15227", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "a326f35b", "metadata": {}, "source": ["[Here](https://python.langchain.com/v0.2/docs/how_to/#chat-models) is a useful how-to for all the things that you can do with chat models, but we'll show a few highlights below. If you've run `pip install -r requirements.txt` as noted in the README, then you've installed the `langchain-openai` package. With this, we can instantiate our `ChatOpenAI` model object. If you are signing up for the API for the first time, you should receive [free credits](https://community.openai.com/t/understanding-api-limits-and-free-tier/498517) that can be applied to any of the models. You can see pricing for various models [here](https://openai.com/api/pricing/). The notebooks will default to `gpt-4o` because it's a good balance of quality, price, and speed [see more here](https://help.openai.com/en/articles/7102672-how-can-i-access-gpt-4-gpt-4-turbo-gpt-4o-and-gpt-4o-mini), but you can also opt for the lower priced `gpt-3.5` series models. \n", "\n", "There are [a few standard parameters](https://python.langchain.com/v0.2/docs/concepts/#chat-models) that we can set with chat models. Two of the most common are:\n", "\n", "* `model`: the name of the model\n", "* `temperature`: the sampling temperature\n", "\n", "`Temperature` controls the randomness or creativity of the model's output where low temperature (close to 0) is more deterministic and focused outputs. This is good for tasks requiring accuracy or factual responses. High temperature (close to 1) is good for creative tasks or generating varied responses. "]}, {"cell_type": "code", "execution_count": 2, "id": "e19a54d3", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "gpt4o_chat = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "gpt35_chat = ChatOpenAI(model=\"gpt-3.5-turbo-0125\", temperature=0)"]}, {"cell_type": "markdown", "id": "28450d1b", "metadata": {}, "source": ["Chat models in LangChain have a number of [default methods](https://python.langchain.com/v0.2/docs/concepts/#runnable-interface). For the most part, we'll be using:\n", "\n", "* `stream`: stream back chunks of the response\n", "* `invoke`: call the chain on an input\n", "\n", "And, as mentioned, chat models take [messages](https://python.langchain.com/v0.2/docs/concepts/#messages) as input. Messages have a role (that describes who is saying the message) and a content property. We'll be talking a lot more about this later, but here let's just show the basics."]}, {"cell_type": "code", "execution_count": 3, "id": "b1280e1b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 9, 'prompt_tokens': 11, 'total_tokens': 20}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5', 'finish_reason': 'stop', 'logprobs': None}, id='run-d3c4bc85-ef14-49f6-ba7e-91bf455cffee-0', usage_metadata={'input_tokens': 11, 'output_tokens': 9, 'total_tokens': 20})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "# Create a message\n", "msg = HumanMessage(content=\"Hello world\", name=\"<PERSON>\")\n", "\n", "# Message list\n", "messages = [msg]\n", "\n", "# Invoke the model with a list of messages \n", "gpt4o_chat.invoke(messages)"]}, {"cell_type": "markdown", "id": "cac73e4c", "metadata": {}, "source": ["We get an `AIMessage` response. Also, note that we can just invoke a chat model with a string. When a string is passed in as input, it is converted to a `HumanMessage` and then passed to the underlying model.\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f27c6c9a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 9, 'prompt_tokens': 9, 'total_tokens': 18}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5', 'finish_reason': 'stop', 'logprobs': None}, id='run-d6f6b682-e29a-44de-b45e-79fad1e405e5-0', usage_metadata={'input_tokens': 9, 'output_tokens': 9, 'total_tokens': 18})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["gpt4o_chat.invoke(\"hello world\")"]}, {"cell_type": "code", "execution_count": 5, "id": "fdc2f0ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Hello! How can I assist you today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 9, 'prompt_tokens': 9, 'total_tokens': 18}, 'model_name': 'gpt-3.5-turbo-0125', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-c75d3f0f-2d71-47be-b14c-42b8dd2b4b08-0', usage_metadata={'input_tokens': 9, 'output_tokens': 9, 'total_tokens': 18})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["gpt35_chat.invoke(\"hello world\")"]}, {"cell_type": "markdown", "id": "582c0e5a", "metadata": {}, "source": ["The interface is consistent across all chat models and models are typically initialized once at the start up each notebooks. \n", "\n", "So, you can easily switch between models without changing the downstream code if you have strong preference for another provider.\n"]}, {"cell_type": "markdown", "id": "3ad0069a", "metadata": {}, "source": ["## Search Tools\n", "\n", "You'll also see [<PERSON><PERSON>](https://tavily.com/) in the README, which is a search engine optimized for LLMs and RAG, aimed at efficient, quick, and persistent search results. As mentioned, it's easy to sign up and offers a generous free tier. Some lessons (in Module 4) will use <PERSON><PERSON> by default but, of course, other search tools can be used if you want to modify the code for yourself."]}, {"cell_type": "code", "execution_count": 10, "id": "091dff13", "metadata": {}, "outputs": [], "source": ["_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "code", "execution_count": 6, "id": "52d69da9", "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.tavily_search import TavilySearchResults\n", "tavily_search = TavilySearchResults(max_results=3)\n", "search_docs = tavily_search.invoke(\"What is LangGraph?\")"]}, {"cell_type": "code", "execution_count": 7, "id": "d06f87e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'url': 'https://www.datacamp.com/tutorial/langgraph-tutorial',\n", "  'content': 'LangGraph is a library within the LangChain ecosystem designed to tackle these challenges head-on. LangGraph provides a framework for defining, coordinating, and executing multiple LLM agents (or chains) in a structured manner.'},\n", " {'url': 'https://langchain-ai.github.io/langgraph/',\n", "  'content': 'Overview LangGraph is a library for building stateful, multi-actor applications with LLMs, used to create agent and multi-agent workflows. Compared to other LLM frameworks, it offers these core benefits: cycles, controllability, and persistence. LangGraph allows you to define flows that involve cycles, essential for most agentic architectures, differentiating it from DAG-based solutions. As a ...'},\n", " {'url': 'https://www.youtube.com/watch?v=nmDFSVRnr4Q',\n", "  'content': 'LangGraph is an extension of LangChain enabling Multi-Agent conversation and cyclic chains. This video explains the basics of LangGraph and codesLangChain in...'}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["search_docs"]}, {"cell_type": "code", "execution_count": null, "id": "bafd7d5d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}