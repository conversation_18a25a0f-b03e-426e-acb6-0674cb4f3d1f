{"cells": [{"attachments": {"3a5ede4f-7a62-4e05-9e44-301465ca8555.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["# Connecting to a LangGraph Platform Deployment\n", "\n", "## Deployment Creation\n", "\n", "We just created a [deployment](https://langchain-ai.github.io/langgraph/how-tos/deploy-self-hosted/#how-to-do-a-self-hosted-deployment-of-langgraph) for the `task_maistro` app from module 5.\n", "\n", "* We used the [the LangGraph CLI](https://langchain-ai.github.io/langgraph/concepts/langgraph_cli/#commands) to build a Docker image for the LangGraph Server with our `task_maistro` graph.\n", "* We used the provided `docker-compose.yml` file to create three separate containers based on the services defined: \n", "    * `langgraph-redis`: Creates a new container using the official Redis image.\n", "    * `langgraph-postgres`: Creates a new container using the official Postgres image.\n", "    * `langgraph-api`: Creates a new container using our pre-built `task_maistro` Docker image.\n", "\n", "```\n", "$ cd module-6/deployment\n", "$ docker compose up\n", "```\n", "\n", "Once running, we can access the deployment through:\n", "      \n", "* API: http://localhost:8123\n", "* Docs: http://localhost:8123/docs\n", "* LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:8123\n", "\n", "![langgraph-platform-high-level.png](attachment:3a5ede4f-7a62-4e05-9e44-301465ca8555.png)\n", "\n", "## Using the API  \n", "\n", "LangGraph Server exposes [many API endpoints](https://github.com/langchain-ai/agent-protocol) for interacting with the deployed agent.\n", "\n", "We can group [these endpoints into a few common agent needs](https://github.com/langchain-ai/agent-protocol): \n", "\n", "* **Runs**: Atomic agent executions\n", "* **Threads**: Multi-turn interactions or human in the loop\n", "* **Store**: Long-term memory\n", "\n", "We can test requests directly [in the API docs](http://localhost:8123/docs#tag/thread-runs)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## SDK\n", "\n", "The [LangGraph SDKs](https://langchain-ai.github.io/langgraph/concepts/sdk/) (Python and JS) provide a developer-friendly interface to interact with the LangGraph Server API presented above."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph_sdk"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "\n", "# Connect via SDK\n", "url_for_cli_deployment = \"http://localhost:8123\"\n", "client = get_client(url=url_for_cli_deployment)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Remote Graph\n", "\n", "If you are working in the LangGraph library, [Remote Graph](https://langchain-ai.github.io/langgraph/how-tos/use-remote-graph/) is also a useful way to connect directly to the graph."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langchain_openai langgraph langchain_core"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langgraph.pregel.remote import RemoteGraph\n", "from langchain_core.messages import convert_to_messages\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "# Connect via remote graph\n", "url = \"http://localhost:8123\"\n", "graph_name = \"task_maistro\" \n", "remote_graph = RemoteGraph(graph_name, url=url)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Runs\n", "\n", "A \"run\" represents a [single execution](https://github.com/langchain-ai/agent-protocol?tab=readme-ov-file#runs-atomic-agent-executions) of your graph. Each time a client makes a request:\n", "\n", "1. The HTTP worker generates a unique run ID\n", "2. This run and its results are stored in PostgreSQL\n", "3. You can query these runs to:\n", "   - Check their status\n", "   - Get their results\n", "   - Track execution history\n", "\n", "You can see a full set of How To guides for various types of runs [here](https://langchain-ai.github.io/langgraph/how-tos/#runs).\n", "\n", "Let's looks at a few of the interesting things we can do with runs.\n", "\n", "### Background Runs\n", "\n", "The LangGraph server supports two types of runs: \n", "\n", "* `Fire and forget` - Launch a run in the background, but don’t wait for it to finish\n", "* `Waiting on a reply (blocking or polling)` - Launch a run and wait/stream its output\n", "\n", "Background runs and polling are quite useful when working with long-running agents. \n", "\n", "Let's [see](https://langchain-ai.github.io/langgraph/cloud/how-tos/background_run/#check-runs-on-thread) how this works:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'thread_id': '7f71c0dd-768b-4e53-8349-42bdd10e7caf',\n", " 'created_at': '2024-11-14T19:36:08.459457+00:00',\n", " 'updated_at': '2024-11-14T19:36:08.459457+00:00',\n", " 'metadata': {},\n", " 'status': 'idle',\n", " 'config': {},\n", " 'values': None}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a thread\n", "thread = await client.threads.create()\n", "thread"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n"]}], "source": ["# Check any existing runs on a thread\n", "thread = await client.threads.create()\n", "runs = await client.runs.list(thread[\"thread_id\"])\n", "print(runs)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Ensure we've created some ToDos and saved them to my user_id\n", "user_input = \"Add a ToDo to finish booking travel to Hong Kong by end of next week. Also, add a ToDo to call parents back about Thanksgiving plans.\"\n", "config = {\"configurable\": {\"user_id\": \"Test\"}}\n", "graph_name = \"task_maistro\" \n", "run = await client.runs.create(thread[\"thread_id\"], graph_name, input={\"messages\": [HumanMessage(content=user_input)]}, config=config)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Kick off a new thread and a new run\n", "thread = await client.threads.create()\n", "user_input = \"Give me a summary of all ToDos.\"\n", "config = {\"configurable\": {\"user_id\": \"Test\"}}\n", "graph_name = \"task_maistro\" \n", "run = await client.runs.create(thread[\"thread_id\"], graph_name, input={\"messages\": [HumanMessage(content=user_input)]}, config=config)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'run_id': '1efa2c00-63e4-6f4a-9c5b-ca3f5f9bff07', 'thread_id': '641c195a-9e31-4250-a729-6b742c089df8', 'assistant_id': 'ea4ebafa-a81d-5063-a5fa-67c755d98a21', 'created_at': '2024-11-14T19:38:29.394777+00:00', 'updated_at': '2024-11-14T19:38:29.394777+00:00', 'metadata': {}, 'status': 'pending', 'kwargs': {'input': {'messages': [{'id': None, 'name': None, 'type': 'human', 'content': 'Give me a summary of all ToDos.', 'example': False, 'additional_kwargs': {}, 'response_metadata': {}}]}, 'config': {'metadata': {'created_by': 'system'}, 'configurable': {'run_id': '1efa2c00-63e4-6f4a-9c5b-ca3f5f9bff07', 'user_id': 'Test', 'graph_id': 'task_maistro', 'thread_id': '641c195a-9e31-4250-a729-6b742c089df8', 'assistant_id': 'ea4ebafa-a81d-5063-a5fa-67c755d98a21'}}, 'webhook': None, 'subgraphs': False, 'temporary': False, 'stream_mode': ['values'], 'feedback_keys': None, 'interrupt_after': None, 'interrupt_before': None}, 'multitask_strategy': 'reject'}\n"]}], "source": ["# Check the run status\n", "print(await client.runs.get(thread[\"thread_id\"], run[\"run_id\"]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that it has `'status': 'pending'` because it is still running.\n", "\n", "What if we want to wait until the run completes, making it a blocking run?\n", "\n", "We can use `client.runs.join` to wait until the run completes.\n", "\n", "This ensures that no new runs are started until the current run completes on the thread."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'run_id': '1efa2c00-63e4-6f4a-9c5b-ca3f5f9bff07', 'thread_id': '641c195a-9e31-4250-a729-6b742c089df8', 'assistant_id': 'ea4ebafa-a81d-5063-a5fa-67c755d98a21', 'created_at': '2024-11-14T19:38:29.394777+00:00', 'updated_at': '2024-11-14T19:38:29.394777+00:00', 'metadata': {}, 'status': 'success', 'kwargs': {'input': {'messages': [{'id': None, 'name': None, 'type': 'human', 'content': 'Give me a summary of all ToDos.', 'example': False, 'additional_kwargs': {}, 'response_metadata': {}}]}, 'config': {'metadata': {'created_by': 'system'}, 'configurable': {'run_id': '1efa2c00-63e4-6f4a-9c5b-ca3f5f9bff07', 'user_id': 'Test', 'graph_id': 'task_maistro', 'thread_id': '641c195a-9e31-4250-a729-6b742c089df8', 'assistant_id': 'ea4ebafa-a81d-5063-a5fa-67c755d98a21'}}, 'webhook': None, 'subgraphs': False, 'temporary': False, 'stream_mode': ['values'], 'feedback_keys': None, 'interrupt_after': None, 'interrupt_before': None}, 'multitask_strategy': 'reject'}\n"]}], "source": ["# Wait until the run completes\n", "await client.runs.join(thread[\"thread_id\"], run[\"run_id\"])\n", "print(await client.runs.get(thread[\"thread_id\"], run[\"run_id\"]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now the run has `'status': 'success'` because it has completed."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming Runs\n", "\n", "Each time a client makes a streaming request:\n", "\n", "1. The HTTP worker generates a unique run ID\n", "2. The Queue worker begins work on the run \n", "3. During execution, the Queue worker publishes update to Redis\n", "4. The HTTP worker subscribes to updates from Redis for ths run, and returns them to the client \n", "\n", "This enabled streaming! \n", "\n", "We've covered [streaming](https://langchain-ai.github.io/langgraph/how-tos/#streaming_1) in previous modules, but let's pick one method -- streaming tokens -- to highlight.\n", "\n", "Streaming tokens back to the client is especially useful when working with production agents that may take a while to complete.\n", "\n", "We [stream tokens](https://langchain-ai.github.io/langgraph/cloud/how-tos/stream_messages/#setup) using `stream_mode=\"messages-tuple\"`."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You might want to focus on \"Call parents back about Thanksgiving plans\" first. It has a shorter estimated time to complete (15 minutes) and doesn't have a specific deadline, so it could be a quick task to check off your list. Once that's done, you can dedicate more time to \"Finish booking travel to Hong Kong,\" which is more time-consuming and has a deadline."]}], "source": ["user_input = \"What ToD<PERSON> should I focus on first.\"\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      graph_name, \n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      config=config,\n", "                                      stream_mode=\"messages-tuple\"):\n", "\n", "    if chunk.event == \"messages\":\n", "        print(\"\".join(data_item['content'] for data_item in chunk.data if 'content' in data_item), end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Threads\n", "\n", "Whereas a run is only a single execution of the graph, a thread supports *multi-turn* interactions.\n", "\n", "When the client makes a graph execution execution with a `thread_id`, the server will save all [checkpoints](https://langchain-ai.github.io/langgraph/concepts/persistence/#checkpoints) (steps) in the run to the thread in the Postgres database.\n", "\n", "The server allows us to [check the status of created threads](https://langchain-ai.github.io/langgraph/cloud/how-tos/check_thread_status/).\n", "\n", "### Check thread state\n", "\n", "In addition, we can easily access the state [checkpoints](https://langchain-ai.github.io/langgraph/concepts/persistence/#checkpoints) saved to any specific thread."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Give me a summary of all ToDos.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Here's a summary of your current ToDo list:\n", "\n", "1. **Task:** Finish booking travel to Hong Kong\n", "   - **Status:** Not started\n", "   - **Deadline:** November 22, 2024\n", "   - **Solutions:** \n", "     - Check flight prices on Skyscanner\n", "     - Book hotel through Booking.com\n", "     - Arrange airport transfer\n", "   - **Estimated Time to Complete:** 120 minutes\n", "\n", "2. **Task:** Call parents back about Thanksgiving plans\n", "   - **Status:** Not started\n", "   - **Deadline:** None\n", "   - **Solutions:** \n", "     - Check calendar for availability\n", "     - Discuss travel arrangements\n", "     - Confirm dinner plans\n", "   - **Estimated Time to Complete:** 15 minutes\n", "\n", "Let me know if there's anything else you'd like to do with your ToDo list!\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What To<PERSON><PERSON> should I focus on first.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "You might want to focus on \"Call parents back about Thanksgiving plans\" first. It has a shorter estimated time to complete (15 minutes) and doesn't have a specific deadline, so it could be a quick task to check off your list. Once that's done, you can dedicate more time to \"Finish booking travel to Hong Kong,\" which is more time-consuming and has a deadline.\n"]}], "source": ["thread_state = await client.threads.get_state(thread['thread_id'])\n", "for m in convert_to_messages(thread_state['values']['messages']):\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Copy threads\n", "\n", "We can also [copy](https://langchain-ai.github.io/langgraph/cloud/how-tos/copy_threads/) (i.e. \"fork\") an existing thread. \n", "\n", "This will keep the existing thread's history, but allow us to create independent runs that do not affect the original thread."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Copy the thread\n", "copied_thread = await client.threads.copy(thread['thread_id'])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Give me a summary of all ToDos.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Here's a summary of your current ToDo list:\n", "\n", "1. **Task:** Finish booking travel to Hong Kong\n", "   - **Status:** Not started\n", "   - **Deadline:** November 22, 2024\n", "   - **Solutions:** \n", "     - Check flight prices on Skyscanner\n", "     - Book hotel through Booking.com\n", "     - Arrange airport transfer\n", "   - **Estimated Time to Complete:** 120 minutes\n", "\n", "2. **Task:** Call parents back about Thanksgiving plans\n", "   - **Status:** Not started\n", "   - **Deadline:** None\n", "   - **Solutions:** \n", "     - Check calendar for availability\n", "     - Discuss travel arrangements\n", "     - Confirm dinner plans\n", "   - **Estimated Time to Complete:** 15 minutes\n", "\n", "Let me know if there's anything else you'd like to do with your ToDo list!\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What To<PERSON><PERSON> should I focus on first.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "You might want to focus on \"Call parents back about Thanksgiving plans\" first. It has a shorter estimated time to complete (15 minutes) and doesn't have a specific deadline, so it could be a quick task to check off your list. Once that's done, you can dedicate more time to \"Finish booking travel to Hong Kong,\" which is more time-consuming and has a deadline.\n"]}], "source": ["# Check the state of the copied thread\n", "copied_thread_state = await client.threads.get_state(copied_thread['thread_id'])\n", "for m in convert_to_messages(copied_thread_state['values']['messages']):\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Human in the loop\n", "\n", "We covered [Human in the loop](https://langchain-ai.github.io/langgraph/how-tos/human_in_the_loop/) in Module 3, and the server supports all Human in the loop features that we discussed.\n", "\n", "As an example, [we can search, edit, and continue graph execution](https://langchain-ai.github.io/langgraph/concepts/persistence/#capabilities) from any prior checkpoint. "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [{'content': 'Give me a summary of all ToDos.',\n", "   'additional_kwargs': {'example': False,\n", "    'additional_kwargs': {},\n", "    'response_metadata': {}},\n", "   'response_metadata': {},\n", "   'type': 'human',\n", "   'name': None,\n", "   'id': '3680da45-e3a5-4a47-b5b1-4fd4d3e8baf9',\n", "   'example': False}]}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get the history of the thread\n", "states = await client.threads.get_history(thread['thread_id'])\n", "\n", "# Pick a state update to fork\n", "to_fork = states[-2]\n", "to_fork['values']"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["'3680da45-e3a5-4a47-b5b1-4fd4d3e8baf9'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["to_fork['values']['messages'][0]['id']"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["['task_mAIstro']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["to_fork['next']"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1efa2c00-6609-67ff-8000-491b1dcf8129'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["to_fork['checkpoint_id']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's edit the state. Remember how our reducer on `messages` works: \n", "\n", "* It will append, unless we supply a message ID.\n", "* We supply the message ID to overwrite the message, rather than appending to state!"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["forked_input = {\"messages\": HumanMessage(content=\"Give me a summary of all ToDos that need to be done in the next week.\",\n", "                                         id=to_fork['values']['messages'][0]['id'])}\n", "\n", "# Update the state, creating a new checkpoint in the thread\n", "forked_config = await client.threads.update_state(\n", "    thread[\"thread_id\"],\n", "    forked_input,\n", "    checkpoint_id=to_fork['checkpoint_id']\n", ")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's a summary of your ToDos that need to be done in the next week:\n", "\n", "1. **Finish booking travel to Hong Kong**\n", "   - **Status:** Not started\n", "   - **Deadline:** November 22, 2024\n", "   - **Solutions:** \n", "     - Check flight prices on Skyscanner\n", "     - Book hotel through Booking.com\n", "     - Arrange airport transfer\n", "   - **Estimated Time to Complete:** 120 minutes\n", "\n", "It looks like this task is due soon, so you might want to prioritize it. Let me know if there's anything else you need help with!"]}], "source": ["# Run the graph from the new checkpoint in the thread\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      graph_name, \n", "                                      input=None,\n", "                                      config=config,\n", "                                      checkpoint_id=forked_config['checkpoint_id'],\n", "                                      stream_mode=\"messages-tuple\"):\n", "\n", "    if chunk.event == \"messages\":\n", "        print(\"\".join(data_item['content'] for data_item in chunk.data if 'content' in data_item), end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Across-thread memory\n", "\n", "In module 5, we covered how the [LangGraph memory `store`](https://langchain-ai.github.io/langgraph/concepts/persistence/#memory-store) can be used to save information across threads.\n", "\n", "Our deployed graph, `task_maistro`, uses the `store` to save information -- such as ToDos -- namespaced to the `user_id`.\n", "\n", "Our deployment includes a Postgres database, which stores these long-term (across-thread) memories.\n", "\n", "There are several methods available [for interacting with the store](https://langchain-ai.github.io/langgraph/cloud/reference/sdk/python_sdk_ref/#langgraph_sdk.client.StoreClient) in our deployment using the LangGraph SDK.\n", "\n", "### Search items\n", "\n", "The `task_maistro` graph uses the `store` to save ToDos namespaced by default to (`todo`, `todo_category`, `user_id`). \n", "\n", "The `todo_category` is by default set to `general` (as you can see in `deployment/configuration.py`).\n", "\n", "We can simply supply this tuple to search for all ToDos. "]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'value': {'task': 'Finish booking travel to Hong Kong',\n", "   'status': 'not started',\n", "   'deadline': '2024-11-22T23:59:59',\n", "   'solutions': ['Check flight prices on Skyscanner',\n", "    'Book hotel through Booking.com',\n", "    'Arrange airport transfer'],\n", "   'time_to_complete': 120},\n", "  'key': '18524803-c182-49de-9b10-08ccb0a06843',\n", "  'namespace': ['todo', 'general', 'Test'],\n", "  'created_at': '2024-11-14T19:37:41.664827+00:00',\n", "  'updated_at': '2024-11-14T19:37:41.664827+00:00'},\n", " {'value': {'task': 'Call parents back about Thanksgiving plans',\n", "   'status': 'not started',\n", "   'deadline': None,\n", "   'solutions': ['Check calendar for availability',\n", "    'Discuss travel arrangements',\n", "    'Confirm dinner plans'],\n", "   'time_to_complete': 15},\n", "  'key': '375d9596-edf8-4de2-985b-bacdc623d6ef',\n", "  'namespace': ['todo', 'general', 'Test'],\n", "  'created_at': '2024-11-14T19:37:41.664827+00:00',\n", "  'updated_at': '2024-11-14T19:37:41.664827+00:00'}]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["items = await client.store.search_items(\n", "    (\"todo\", \"general\", \"Test\"),\n", "    limit=5,\n", "    offset=0\n", ")\n", "items['items']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add items\n", "\n", "In our graph, we call `put` to add items to the store.\n", "\n", "We can use [put](https://langchain-ai.github.io/langgraph/cloud/reference/sdk/python_sdk_ref/#langgraph_sdk.client.StoreClient.put_item) with the SDK if we want to directly add items to the store outside our graph."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from uuid import uuid4\n", "await client.store.put_item(\n", "    (\"testing\", \"Test\"),\n", "    key=str(uuid4()),\n", "    value={\"todo\": \"Test SDK put_item\"},\n", ")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'value': {'todo': 'Test SDK put_item'},\n", "  'key': '3de441ba-8c79-4beb-8f52-00e4dcba68d4',\n", "  'namespace': ['testing', 'Test'],\n", "  'created_at': '2024-11-14T19:56:30.452808+00:00',\n", "  'updated_at': '2024-11-14T19:56:30.452808+00:00'},\n", " {'value': {'todo': 'Test SDK put_item'},\n", "  'key': '09b9a869-4406-47c5-a635-4716bd79a8b3',\n", "  'namespace': ['testing', 'Test'],\n", "  'created_at': '2024-11-14T19:53:24.812558+00:00',\n", "  'updated_at': '2024-11-14T19:53:24.812558+00:00'}]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["items = await client.store.search_items(\n", "    (\"testing\", \"Test\"),\n", "    limit=5,\n", "    offset=0\n", ")\n", "items['items']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delete items\n", "\n", "We can use the SDK to [delete items](https://langchain-ai.github.io/langgraph/cloud/reference/sdk/python_sdk_ref/#langgraph_sdk.client.StoreClient.delete_item) from the store by key."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["['3de441ba-8c79-4beb-8f52-00e4dcba68d4',\n", " '09b9a869-4406-47c5-a635-4716bd79a8b3']"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["[item['key'] for item in items['items']]"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["await client.store.delete_item(\n", "       (\"testing\", \"Test\"),\n", "        key='3de441ba-8c79-4beb-8f52-00e4dcba68d4',\n", "    )"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'value': {'todo': 'Test SDK put_item'},\n", "  'key': '09b9a869-4406-47c5-a635-4716bd79a8b3',\n", "  'namespace': ['testing', 'Test'],\n", "  'created_at': '2024-11-14T19:53:24.812558+00:00',\n", "  'updated_at': '2024-11-14T19:53:24.812558+00:00'}]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["items = await client.store.search_items(\n", "    (\"testing\", \"Test\"),\n", "    limit=5,\n", "    offset=0\n", ")\n", "items['items']"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}