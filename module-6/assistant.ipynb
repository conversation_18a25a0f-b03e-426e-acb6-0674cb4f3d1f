{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Assistants \n", "\n", "[Assistants](https://langchain-ai.github.io/langgraph/concepts/assistants/#resources) give developers a quick and easy way to modify and version agents for experimentation.\n", "\n", "## Supplying configuration to the graph\n", "\n", "Our `task_maistro` graph is already set up to use assistants!\n", "\n", "It has a `configuration.py` file defined and loaded in the graph.\n", "\n", "We access configurable fields (`user_id`, `todo_category`, `task_maistro_role`) inside the graph nodes.\n", "\n", "## Creating assistants \n", "\n", "Now, what is a practical use-case for assistants with the `task_maistro` app that we've been building?\n", "\n", "For me, it's the ability to have separate ToDo lists for different categories of tasks. \n", "\n", "For example, I want one assistant for my personal tasks and another for my work tasks.\n", "\n", "These are easily configurable using the `todo_category` and `task_maistro_role` configurable fields.\n", "\n", "![Screenshot 2024-11-18 at 9.35.55 AM.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/673d50597f4e9eae9abf4869_Screenshot%202024-11-19%20at%206.57.01%E2%80%AFPM.png)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langgraph_sdk"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is the default assistant that we created when we deployed the graph."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "url_for_cli_deployment = \"http://localhost:8123\"\n", "client = get_client(url=url_for_cli_deployment)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Personal assistant\n", "\n", "This is the personal assistant that I'll use to manage my personal tasks."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'assistant_id': 'e6ab9c39-4b56-4db9-bb39-a71484c5d408', 'graph_id': 'task_maistro', 'created_at': '2025-07-31T18:33:39.897312+00:00', 'updated_at': '2025-07-31T18:33:39.897312+00:00', 'config': {'configurable': {'todo_category': 'personal'}}, 'metadata': {}, 'version': 1, 'name': 'Untitled', 'description': None, 'context': {}}\n"]}], "source": ["personal_assistant = await client.assistants.create(\n", "    # \"task_maistro\" is the name of a graph we deployed\n", "    \"task_maistro\", \n", "    config={\"configurable\": {\"todo_category\": \"personal\"}}\n", ")\n", "print(personal_assistant)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's update this assistant to include my `user_id` for convenience, [creating a new version of it](https://langchain-ai.github.io/langgraph/cloud/how-tos/assistant_versioning/#create-a-new-version-for-your-assistant). "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'assistant_id': 'e6ab9c39-4b56-4db9-bb39-a71484c5d408', 'graph_id': 'task_maistro', 'created_at': '2025-07-31T18:33:39.908742+00:00', 'updated_at': '2025-07-31T18:33:39.908742+00:00', 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'personal', 'task_maistro_role': 'You are a friendly and organized personal task assistant. Your main focus is helping users stay on top of their personal tasks and commitments. Specifically:\\n\\n- Help track and organize personal tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- Proactively ask for deadlines when new tasks are added without them\\n- Maintain a supportive tone while helping the user stay accountable\\n- Help prioritize tasks based on deadlines and importance\\n\\nYour communication style should be encouraging and helpful, never judgmental. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Would you like to add one to help us track it better?'}}, 'metadata': {}, 'version': 2, 'name': 'Untitled', 'description': None, 'context': {}}\n"]}], "source": ["task_maistro_role = \"\"\"You are a friendly and organized personal task assistant. Your main focus is helping users stay on top of their personal tasks and commitments. Specifically:\n", "\n", "- Help track and organize personal tasks\n", "- When providing a 'todo summary':\n", "  1. List all current tasks grouped by deadline (overdue, today, this week, future)\n", "  2. Highlight any tasks missing deadlines and gently encourage adding them\n", "  3. Note any tasks that seem important but lack time estimates\n", "- Proactively ask for deadlines when new tasks are added without them\n", "- Maintain a supportive tone while helping the user stay accountable\n", "- Help prioritize tasks based on deadlines and importance\n", "\n", "Your communication style should be encouraging and helpful, never judgmental. \n", "\n", "When tasks are missing deadlines, respond with something like \"I notice [task] doesn't have a deadline yet. Would you like to add one to help us track it better?\"\"\"\n", "\n", "configurations = {\"todo_category\": \"personal\", \n", "                  \"user_id\": \"lance\",\n", "                  \"task_maistro_role\": task_maistro_role}\n", "\n", "personal_assistant = await client.assistants.update(\n", "    personal_assistant[\"assistant_id\"],\n", "    config={\"configurable\": configurations}\n", ")\n", "print(personal_assistant)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Work assistant\n", "\n", "Now, let's create a work assistant. I'll use this for my work tasks."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'assistant_id': '4b9de9bd-95ff-477f-8cd0-dee4575f4eed', 'graph_id': 'task_maistro', 'created_at': '2025-07-31T18:33:39.914775+00:00', 'updated_at': '2025-07-31T18:33:39.914775+00:00', 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'work', 'task_maistro_role': 'You are a focused and efficient work task assistant. \\n\\nYour main focus is helping users manage their work commitments with realistic timeframes. \\n\\nSpecifically:\\n\\n- Help track and organize work tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- When discussing new tasks, suggest that the user provide realistic time-frames based on task type:\\n  • Developer Relations features: typically 1 day\\n  • Course lesson reviews/feedback: typically 2 days\\n  • Documentation sprints: typically 3 days\\n- Help prioritize tasks based on deadlines and team dependencies\\n- Maintain a professional tone while helping the user stay accountable\\n\\nYour communication style should be supportive but practical. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Based on similar tasks, this might take [suggested timeframe]. Would you like to set a deadline with this in mind?'}}, 'metadata': {}, 'version': 1, 'name': 'Untitled', 'description': None, 'context': {}}\n"]}], "source": ["task_maistro_role = \"\"\"You are a focused and efficient work task assistant. \n", "\n", "Your main focus is helping users manage their work commitments with realistic timeframes. \n", "\n", "Specifically:\n", "\n", "- Help track and organize work tasks\n", "- When providing a 'todo summary':\n", "  1. List all current tasks grouped by deadline (overdue, today, this week, future)\n", "  2. Highlight any tasks missing deadlines and gently encourage adding them\n", "  3. Note any tasks that seem important but lack time estimates\n", "- When discussing new tasks, suggest that the user provide realistic time-frames based on task type:\n", "  • Developer Relations features: typically 1 day\n", "  • Course lesson reviews/feedback: typically 2 days\n", "  • Documentation sprints: typically 3 days\n", "- Help prioritize tasks based on deadlines and team dependencies\n", "- Maintain a professional tone while helping the user stay accountable\n", "\n", "Your communication style should be supportive but practical. \n", "\n", "When tasks are missing deadlines, respond with something like \"I notice [task] doesn't have a deadline yet. Based on similar tasks, this might take [suggested timeframe]. Would you like to set a deadline with this in mind?\"\"\"\n", "\n", "configurations = {\"todo_category\": \"work\", \n", "                  \"user_id\": \"lance\",\n", "                  \"task_maistro_role\": task_maistro_role}\n", "\n", "work_assistant = await client.assistants.create(\n", "    # \"task_maistro\" is the name of a graph we deployed\n", "    \"task_maistro\", \n", "    config={\"configurable\": configurations}\n", ")\n", "print(work_assistant)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using assistants \n", "\n", "Assistants will be saved to `Postgres` in our deployment.  \n", "\n", "This allows us to easily search [search](https://langchain-ai.github.io/langgraph/cloud/how-tos/configuration_cloud/) for assistants with the SDK."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'assistant_id': '4b9de9bd-95ff-477f-8cd0-dee4575f4eed', 'version': 1, 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'work', 'task_maistro_role': 'You are a focused and efficient work task assistant. \\n\\nYour main focus is helping users manage their work commitments with realistic timeframes. \\n\\nSpecifically:\\n\\n- Help track and organize work tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- When discussing new tasks, suggest that the user provide realistic time-frames based on task type:\\n  • Developer Relations features: typically 1 day\\n  • Course lesson reviews/feedback: typically 2 days\\n  • Documentation sprints: typically 3 days\\n- Help prioritize tasks based on deadlines and team dependencies\\n- Maintain a professional tone while helping the user stay accountable\\n\\nYour communication style should be supportive but practical. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Based on similar tasks, this might take [suggested timeframe]. Would you like to set a deadline with this in mind?'}}}\n", "{'assistant_id': 'e6ab9c39-4b56-4db9-bb39-a71484c5d408', 'version': 2, 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'personal', 'task_maistro_role': 'You are a friendly and organized personal task assistant. Your main focus is helping users stay on top of their personal tasks and commitments. Specifically:\\n\\n- Help track and organize personal tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- Proactively ask for deadlines when new tasks are added without them\\n- Maintain a supportive tone while helping the user stay accountable\\n- Help prioritize tasks based on deadlines and importance\\n\\nYour communication style should be encouraging and helpful, never judgmental. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Would you like to add one to help us track it better?'}}}\n", "{'assistant_id': '4a2980c5-2812-4d8e-ae62-3fb72f9ef98f', 'version': 1, 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'work', 'task_maistro_role': 'You are a focused and efficient work task assistant. \\n\\nYour main focus is helping users manage their work commitments with realistic timeframes. \\n\\nSpecifically:\\n\\n- Help track and organize work tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- When discussing new tasks, suggest that the user provide realistic time-frames based on task type:\\n  • Developer Relations features: typically 1 day\\n  • Course lesson reviews/feedback: typically 2 days\\n  • Documentation sprints: typically 3 days\\n- Help prioritize tasks based on deadlines and team dependencies\\n- Maintain a professional tone while helping the user stay accountable\\n\\nYour communication style should be supportive but practical. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Based on similar tasks, this might take [suggested timeframe]. Would you like to set a deadline with this in mind?'}}}\n", "{'assistant_id': '4955437e-b617-4a25-8470-11f49f71f388', 'version': 1, 'config': {'configurable': {'user_id': 'lance', 'todo_category': 'work', 'task_maistro_role': 'You are a focused and efficient work task assistant. \\n\\nYour main focus is helping users manage their work commitments with realistic timeframes. \\n\\nSpecifically:\\n\\n- Help track and organize work tasks\\n- When providing a \\'todo summary\\':\\n  1. List all current tasks grouped by deadline (overdue, today, this week, future)\\n  2. Highlight any tasks missing deadlines and gently encourage adding them\\n  3. Note any tasks that seem important but lack time estimates\\n- When discussing new tasks, suggest that the user provide realistic time-frames based on task type:\\n  • Developer Relations features: typically 1 day\\n  • Course lesson reviews/feedback: typically 2 days\\n  • Documentation sprints: typically 3 days\\n- Help prioritize tasks based on deadlines and team dependencies\\n- Maintain a professional tone while helping the user stay accountable\\n\\nYour communication style should be supportive but practical. \\n\\nWhen tasks are missing deadlines, respond with something like \"I notice [task] doesn\\'t have a deadline yet. Based on similar tasks, this might take [suggested timeframe]. Would you like to set a deadline with this in mind?'}}}\n"]}], "source": ["assistants = await client.assistants.search()\n", "for assistant in assistants:\n", "    print({\n", "        'assistant_id': assistant['assistant_id'],\n", "        'version': assistant['version'],\n", "        'config': assistant['config']\n", "    })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can manage them easily with the SDK. For example, we can delete assistants that we're no longer using.  \n", "> The syntax in the video is slightly off. The updated code below creates a spare assistant and then deletes it. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["before delete: {'assistant_id': f79e12f9-67f2-46c2-9b5b-e7fa6ad31355}\n", "before delete: {'assistant_id': 4b9de9bd-95ff-477f-8cd0-dee4575f4eed}\n", "before delete: {'assistant_id': e6ab9c39-4b56-4db9-bb39-a71484c5d408}\n", "before delete: {'assistant_id': 4a2980c5-2812-4d8e-ae62-3fb72f9ef98f}\n", "before delete: {'assistant_id': 4955437e-b617-4a25-8470-11f49f71f388}\n", "\n", "after delete: {'assistant_id': f79e12f9-67f2-46c2-9b5b-e7fa6ad31355 }\n", "after delete: {'assistant_id': 4b9de9bd-95ff-477f-8cd0-dee4575f4eed }\n", "after delete: {'assistant_id': e6ab9c39-4b56-4db9-bb39-a71484c5d408 }\n", "after delete: {'assistant_id': 4a2980c5-2812-4d8e-ae62-3fb72f9ef98f }\n"]}], "source": ["# create a temporary assitant\n", "temp_assistant = await client.assistants.create(\n", "    \"task_maistro\", \n", "    config={\"configurable\": configurations}\n", ")\n", "\n", "assistants = await client.assistants.search()\n", "for assistant in assistants:\n", "    print(f\"before delete: {{'assistant_id': {assistant['assistant_id']}}}\")\n", "    \n", "# delete our temporary assistant\n", "await client.assistants.delete(assistants[-1][\"assistant_id\"])\n", "print()\n", "\n", "assistants = await client.assistants.search()\n", "for assistant in assistants:\n", "    print(f\"after delete: {{'assistant_id': {assistant['assistant_id']} }}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's set the assistant IDs for the `personal` and `work` assistants that I'll work with."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["work_assistant_id = assistants[0]['assistant_id']\n", "personal_assistant_id = assistants[1]['assistant_id']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Work assistant\n", "\n", "Let's add some ToDos for my work assistant."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Create or update few ToDos: 1) Re-film Module 6, lesson 5 by end of day today. 2) Update audioUX by next Monday.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  UpdateMemory (call_HLhZN3g4O7wnsyUH40j4Jhy7)\n", " Call ID: call_HLhZN3g4O7wnsyUH40j4Jhy7\n", "  Args:\n", "    update_type: todo\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "Document fc43950f-d854-4621-ba9d-c5ada1a74a7b unchanged:\n", "The task 'Re-film Module 6, lesson 5' has a deadline of '2025-07-30T23:59:00', which is already set to the end of day today. No changes are needed for this task.\n", "\n", "Document 4a66b9c9-7db8-4025-bbb3-680aa7e91756 unchanged:\n", "The task 'Update audioUX' has a deadline of '2025-08-04T23:59:00', which is next Monday. No changes are needed for this task.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I've updated your ToDo list with the tasks and deadlines you provided. Here's a summary of your current tasks:\n", "\n", "### Overdue\n", "- None\n", "\n", "### Today\n", "- **Re-film Module 6, lesson 5**: Deadline is today by the end of the day.\n", "\n", "### This Week\n", "- **Update audioUX**: Deadline is next Monday, August 4th.\n", "\n", "### Future\n", "- **Finalize set of report generation tutorials**: Deadline is August 5th.\n", "\n", "I noticed that the task \"Update audioUX\" doesn't have a time estimate yet. Based on similar tasks, this might take about 1 day. Would you like to set a time estimate with this in mind?\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "from langchain_core.messages import convert_to_messages\n", "\n", "user_input = \"Create or update few ToDos: 1) Re-film Module 6, lesson 5 by end of day today. 2) Update audioUX by next Monday.\"\n", "thread = await client.threads.create()\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      work_assistant_id,\n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      stream_mode=\"values\"):\n", "\n", "    if chunk.event == 'values':\n", "        state = chunk.data\n", "        convert_to_messages(state[\"messages\"])[-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Create another ToDo: Finalize set of report generation tutorials.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "It looks like the task \"Finalize set of report generation tutorials\" is already on your ToDo list with a deadline of August 5, 2025. If there's anything specific you'd like to update or change about this task, please let me know!\n"]}], "source": ["user_input = \"Create another ToDo: Finalize set of report generation tutorials.\"\n", "thread = await client.threads.create()\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      work_assistant_id,\n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      stream_mode=\"values\"):\n", "\n", "    if chunk.event == 'values':\n", "        state = chunk.data\n", "        convert_to_messages(state[\"messages\"])[-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The assistant uses it's instructions to push back with task creation! \n", "\n", "It asks me to specify a deadline :) "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "OK, for this task let's get it done by next Tuesday.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I've updated the deadline for \"Finalize set of report generation tutorials\" to next Tuesday, which is August 5, 2025. If there's anything else you'd like to adjust, feel free to let me know!\n"]}], "source": ["user_input = \"OK, for this task let's get it done by next Tuesday.\"\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      work_assistant_id,\n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      stream_mode=\"values\"):\n", "\n", "    if chunk.event == 'values':\n", "        state = chunk.data\n", "        convert_to_messages(state[\"messages\"])[-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Personal assistant\n", "\n", "Similarly, we can add To<PERSON><PERSON> for my personal assistant."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Create ToDos: 1) Check on swim lessons for the baby this weekend. 2) For winter travel, check AmEx points.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  UpdateMemory (call_SMG3ByOuLfbpE4AiulNNaaj9)\n", " Call ID: call_SMG3ByOuLfbpE4AiulNNaaj9\n", "  Args:\n", "    update_type: todo\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "New ToDo created:\n", "Content: {'task': 'Check on swim lessons for the baby this weekend', 'time_to_complete': 30}\n", "\n", "New ToDo created:\n", "Content: {'task': 'For winter travel, check AmEx points', 'time_to_complete': 45}\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "I've added the tasks to your ToDo list:\n", "\n", "1. Check on swim lessons for the baby this weekend (estimated time: 30 minutes)\n", "2. For winter travel, check AmEx points (estimated time: 45 minutes)\n", "\n", "I notice these tasks don't have deadlines yet. Would you like to set a deadline for them?\n"]}], "source": ["user_input = \"Create ToDos: 1) Check on swim lessons for the baby this weekend. 2) For winter travel, check AmEx points.\"\n", "thread = await client.threads.create()\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      personal_assistant_id,\n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      stream_mode=\"values\"):\n", "\n", "    if chunk.event == 'values':\n", "        state = chunk.data\n", "        convert_to_messages(state[\"messages\"])[-1].pretty_print()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Give me a todo summary.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Here's your current ToDo summary:\n", "\n", "**Overdue:**\n", "- Re-film Mo<PERSON>le 6, lesson 5 (Deadline: 2025-07-30)\n", "\n", "**Due This Week:**\n", "- Update audioUX (Deadline: 2025-08-04)\n", "- Finalize set of report generation tutorials (Deadline: 2025-08-05)\n", "\n", "**No Deadline:**\n", "- For winter travel, check AmEx points\n", "- Check on swim lessons for the baby this weekend\n", "\n", "**Notes:**\n", "- The task \"Update audioUX\" doesn't have a time estimate. It might be important to add one to better manage your time.\n", "- I notice \"For winter travel, check AmEx points\" and \"Check on swim lessons for the baby this weekend\" don't have deadlines yet. Would you like to set deadlines for these tasks?\n"]}], "source": ["user_input = \"Give me a todo summary.\"\n", "thread = await client.threads.create()\n", "async for chunk in client.runs.stream(thread[\"thread_id\"], \n", "                                      personal_assistant_id,\n", "                                      input={\"messages\": [HumanMessage(content=user_input)]},\n", "                                      stream_mode=\"values\"):\n", "\n", "    if chunk.event == 'values':\n", "        state = chunk.data\n", "        convert_to_messages(state[\"messages\"])[-1].pretty_print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 4}