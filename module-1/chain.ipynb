{"cells": [{"cell_type": "markdown", "id": "4cbf2458", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-1/chain.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58238466-lesson-4-chain)"]}, {"attachments": {}, "cell_type": "markdown", "id": "ee55d3da-c53a-4c76-b46f-8e0d602e072e", "metadata": {}, "source": ["# Chain\n", "\n", "## Review\n", "\n", "We built a simple graph with nodes, normal edges, and conditional edges.\n", "\n", "## Goals\n", "\n", "Now, let's build up to a simple chain that combines 4 [concepts](https://python.langchain.com/v0.2/docs/concepts/):\n", "\n", "* Using [chat messages](https://python.langchain.com/v0.2/docs/concepts/#messages) as our graph state\n", "* Using [chat models](https://python.langchain.com/v0.2/docs/concepts/#chat-models) in graph nodes\n", "* [Binding tools](https://python.langchain.com/v0.2/docs/concepts/#tools) to our chat model\n", "* [Executing tool calls](https://python.langchain.com/v0.2/docs/concepts/#functiontool-calling) in graph nodes \n", "\n", "![Screenshot 2024-08-21 at 9.24.03 AM.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dbab08dd607b08df5e1101_chain1.png)"]}, {"cell_type": "code", "execution_count": null, "id": "a55e2e80-a718-4aaf-99b9-371157b34a4b", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langchain_openai langchain_core langgraph"]}, {"cell_type": "markdown", "id": "ae5ac2d0-c7b0-4a20-86e5-4b6ed15ec20e", "metadata": {}, "source": ["## Messages\n", "\n", "Chat models can use [`messages`](https://python.langchain.com/v0.2/docs/concepts/#messages), which capture different roles within a conversation. \n", "\n", "LangChain supports various message types, including `HumanMessage`, `AIMessage`, `SystemMessage`, and `ToolMessage`. \n", "\n", "These represent a message from the user, from chat model, for the chat model to instruct behavior, and from a tool call. \n", "\n", "Let's create a list of messages. \n", "\n", "Each message can be supplied with a few things:\n", "\n", "* `content` - content of the message\n", "* `name` - optionally, a message author \n", "* `response_metadata` - optionally, a dict of metadata (e.g., often populated by model provider for `AIMessages`)"]}, {"cell_type": "code", "execution_count": 1, "id": "866b5321-a238-4a9e-af9e-f11a131b5f11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: Model\n", "\n", "So you said you were researching ocean mammals?\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "Name: <PERSON>\n", "\n", "Yes, that's right.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: Model\n", "\n", "Great, what would you like to learn about.\n", "================================\u001b[1m Human Message \u001b[0m=================================\n", "Name: <PERSON>\n", "\n", "I want to learn about the best place to see Orcas in the US.\n"]}], "source": ["from pprint import pprint\n", "from langchain_core.messages import AIMessage, HumanMessage\n", "\n", "messages = [AIMessage(content=f\"So you said you were researching ocean mammals?\", name=\"Model\")]\n", "messages.append(HumanMessage(content=f\"Yes, that's right.\",name=\"<PERSON>\"))\n", "messages.append(AIMessage(content=f\"Great, what would you like to learn about.\", name=\"Model\"))\n", "messages.append(HumanMessage(content=f\"I want to learn about the best place to see Orcas in the US.\", name=\"<PERSON>\"))\n", "\n", "for m in messages:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "0ca48df0-b639-4ff1-a777-ffe2185d991e", "metadata": {}, "source": ["## Chat Models\n", "\n", "[Chat models](https://python.langchain.com/v0.2/docs/concepts/#chat-models) can use a sequence of message as input and support message types, as discussed above.\n", "\n", "There are [many](https://python.langchain.com/v0.2/docs/concepts/#chat-models) to choose from! Let's work with OpenAI. \n", "\n", "Let's check that your `OPENAI_API_KEY` is set and, if not, you will be asked to enter it."]}, {"cell_type": "code", "execution_count": 2, "id": "2652d5ec-7602-4220-bc6e-b90783ab287b", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "ceae53d4-14f5-4bf3-a953-cc465240f5b5", "metadata": {}, "source": ["We can load a chat model and invoke it with out list of messages.\n", "\n", "We can see that the result is an `AIMessage` with specific `response_metadata`."]}, {"cell_type": "code", "execution_count": 2, "id": "95b99ad4-5753-49d3-a916-a9e949722c01", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.messages.ai.AIMessage"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(model=\"gpt-4o\")\n", "result = llm.invoke(messages)\n", "type(result)"]}, {"cell_type": "code", "execution_count": 3, "id": "88d60338-c892-4d04-a83f-878de4a76a6a", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='One of the best places to see orcas in the United States is the Pacific Northwest, particularly around the San Juan Islands in Washington State. Here are some details:\\n\\n1. **San Juan Islands, Washington**: These islands are a renowned spot for whale watching, with orcas frequently spotted between late spring and early fall. The waters around the San Juan Islands are home to both resident and transient orca pods, making it an excellent location for sightings.\\n\\n2. **Puget Sound, Washington**: This area, including places like Seattle and the surrounding waters, offers additional opportunities to see orcas, particularly the Southern Resident killer whale population.\\n\\n3. **Olympic National Park, Washington**: The coastal areas of the park provide a stunning backdrop for spotting orcas, especially during their migration periods.\\n\\nWhen planning a trip for whale watching, consider peak seasons for orca activity and book tours with reputable operators who adhere to responsible wildlife viewing practices. Additionally, land-based spots like Lime Kiln Point State Park, also known as “Whale Watch Park,” on San Juan Island, offer great opportunities for orca watching from shore.', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 228, 'prompt_tokens': 67, 'total_tokens': 295, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-08-06', 'system_fingerprint': 'fp_50cad350e4', 'finish_reason': 'stop', 'logprobs': None}, id='run-57ed2891-c426-4452-b44b-15d0a5c3f225-0', usage_metadata={'input_tokens': 67, 'output_tokens': 228, 'total_tokens': 295, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 4, "id": "c3a29654-6b8e-4eda-9cec-22fabb9b8620", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'token_usage': {'completion_tokens': 228,\n", "  'prompt_tokens': 67,\n", "  'total_tokens': 295,\n", "  'completion_tokens_details': {'accepted_prediction_tokens': 0,\n", "   'audio_tokens': 0,\n", "   'reasoning_tokens': 0,\n", "   'rejected_prediction_tokens': 0},\n", "  'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}},\n", " 'model_name': 'gpt-4o-2024-08-06',\n", " 'system_fingerprint': 'fp_50cad350e4',\n", " 'finish_reason': 'stop',\n", " 'logprobs': None}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["result.response_metadata"]}, {"attachments": {}, "cell_type": "markdown", "id": "4718bd5c-5314-4405-a164-f1fe912ae306", "metadata": {}, "source": ["## Tools\n", "\n", "Tools are useful whenever you want a model to interact with external systems.\n", "\n", "External systems (e.g., APIs) often require a particular input schema or payload, rather than natural language. \n", "\n", "When we bind an API, for example, as a tool we given the model awareness of the required input schema.\n", "\n", "The model will choose to call a tool based upon the natural language input from the user. \n", "\n", "And, it will return an output that adheres to the tool's schema. \n", "\n", "[Many LLM providers support tool calling](https://python.langchain.com/v0.1/docs/integrations/chat/) and [tool calling interface](https://blog.langchain.dev/improving-core-tool-interfaces-and-docs-in-langchain/) in LangChain is simple. \n", " \n", "You can simply pass any Python `function` into `ChatModel.bind_tools(function)`.\n", "\n", "![Screenshot 2024-08-19 at 7.46.28 PM.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dbab08dc1c17a7a57f9960_chain2.png)"]}, {"cell_type": "markdown", "id": "17a942b1", "metadata": {}, "source": ["Let's showcase a simple example of tool calling!\n", " \n", "The `multiply` function is our tool."]}, {"cell_type": "code", "execution_count": 5, "id": "928faf56-1a1a-4c5f-b97d-bd64d8e166d1", "metadata": {}, "outputs": [], "source": ["def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply a and b.\n", "\n", "    Args:\n", "        a: first int\n", "        b: second int\n", "    \"\"\"\n", "    return a * b\n", "\n", "llm_with_tools = llm.bind_tools([multiply])"]}, {"cell_type": "markdown", "id": "8a3f9dba", "metadata": {}, "source": ["If we pass an input - e.g., `\"What is 2 multiplied by 3\"` - we see a tool call returned. \n", "\n", "The tool call has specific arguments that match the input schema of our function along with the name of the function to call.\n", "\n", "```\n", "{'arguments': '{\"a\":2,\"b\":3}', 'name': 'multiply'}\n", "```"]}, {"cell_type": "code", "execution_count": 8, "id": "9edbe13e-cc72-4685-ac97-2ebb4ceb2544", "metadata": {}, "outputs": [], "source": ["tool_call = llm_with_tools.invoke([HumanMessage(content=f\"What is 2 multiplied by 3\", name=\"<PERSON>\")])"]}, {"cell_type": "code", "execution_count": 9, "id": "a78178cb-fa43-45b5-be5e-5a22bda5a5e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'multiply',\n", "  'args': {'a': 2, 'b': 3},\n", "  'id': 'call_lBBBNo5oYpHGRqwxNaNRbsiT',\n", "  'type': 'tool_call'}]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["tool_call.tool_calls"]}, {"cell_type": "markdown", "id": "21c10f9a-2372-486b-9305-55b7c41ecd6e", "metadata": {}, "source": ["## Using messages as state\n", "\n", "With these foundations in place, we can now use [`messages`](https://python.langchain.com/v0.2/docs/concepts/#messages) in our graph state.\n", "\n", "Let's define our state, `MessagesState`, as a `TypedDict` with a single key: `messages`.\n", "\n", "`messages` is simply a list of messages, as we defined above (e.g., `HumanMessage`, etc)."]}, {"cell_type": "code", "execution_count": 9, "id": "3699dd5c-398c-43c7-b496-fd87e55e11ca", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "from langchain_core.messages import AnyMessage\n", "\n", "class MessagesState(TypedDict):\n", "    messages: list[AnyMessage]"]}, {"cell_type": "markdown", "id": "211cba3e-ebba-4b91-a539-1cbc28b4a40e", "metadata": {}, "source": ["## Reducers\n", "\n", "Now, we have a minor problem! \n", "\n", "As we discussed, each node will return a new value for our state key `messages`.\n", "\n", "But, this new value [will override](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers) the prior `messages` value.\n", " \n", "As our graph runs, we want to **append** messages to our `messages` state key.\n", " \n", "We can use [reducer functions](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers) to address this.\n", "\n", "Reducers allow us to specify how state updates are performed.\n", "\n", "If no reducer function is specified, then it is assumed that updates to the key should *override it* as we saw before.\n", " \n", "But, to append messages, we can use the pre-built `add_messages` reducer.\n", "\n", "This ensures that any messages are appended to the existing list of messages.\n", "\n", "We simply need to annotate our `messages` key with the `add_messages` reducer function as metadata."]}, {"cell_type": "code", "execution_count": 10, "id": "6b33eb72-3197-4870-b9a3-0da8056c40c5", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from langgraph.graph.message import add_messages\n", "\n", "class MessagesState(TypedDict):\n", "    messages: Annotated[list[AnyMessage], add_messages]"]}, {"cell_type": "markdown", "id": "3663e574-ba15-46be-a37c-48c8052d693b", "metadata": {}, "source": ["Since having a list of messages in graph state is so common, LangGraph has a pre-built [`MessagesState`](https://langchain-ai.github.io/langgraph/concepts/low_level/#messagesstate)! \n", "\n", "`MessagesState` is defined: \n", "\n", "* With a pre-build single `messages` key\n", "* This is a list of `AnyMessage` objects \n", "* It uses the `add_messages` reducer\n", "\n", "We'll usually use `MessagesState` because it is less verbose than defining a custom `TypedDict`, as shown above."]}, {"cell_type": "code", "execution_count": 13, "id": "9ab516ee-eab1-4856-8210-99f1fe499672", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import MessagesState\n", "\n", "class MessagesState(MessagesState):\n", "    # Add any keys needed beyond messages, which is pre-built \n", "    pass"]}, {"cell_type": "markdown", "id": "36b0fff7-60a2-4582-8f12-3a3ab6633d6c", "metadata": {}, "source": ["To go a bit deeper, we can see how the `add_messages` reducer works in isolation."]}, {"cell_type": "code", "execution_count": 12, "id": "23ffea76-16a5-4053-a1bc-91e0101d91dc", "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='Hello! How can I assist you?', name='Model', id='cd566566-0f42-46a4-b374-fe4d4770ffa7'),\n", " HumanMessage(content=\"I'm looking for information on marine biology.\", name='<PERSON>', id='9b6c4ddb-9de3-4089-8d22-077f53e7e915'),\n", " AIMessage(content='Sure, I can help with that. What specifically are you interested in?', name='Model', id='74a549aa-8b8b-48d4-bdf1-12e98404e44e')]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# Initial state\n", "initial_messages = [AIMessage(content=\"Hello! How can I assist you?\", name=\"Model\"),\n", "                    HumanMessage(content=\"I'm looking for information on marine biology.\", name=\"<PERSON>\")\n", "                   ]\n", "\n", "# New message to add\n", "new_message = AIMessage(content=\"Sure, I can help with that. What specifically are you interested in?\", name=\"Model\")\n", "\n", "# Test\n", "add_messages(initial_messages , new_message)"]}, {"cell_type": "markdown", "id": "485adccc-f262-49dd-af4f-a30e9b6a48e2", "metadata": {}, "source": ["## Our graph\n", "\n", "Now, lets use `MessagesState` with a graph."]}, {"cell_type": "code", "execution_count": 14, "id": "b5306639-7e6a-44be-8471-8d2631701cfb", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langgraph.graph import StateGraph, START, END\n", "    \n", "# Node\n", "def tool_calling_llm(state: MessagesState):\n", "    return {\"messages\": [llm_with_tools.invoke(state[\"messages\"])]}\n", "\n", "# Build graph\n", "builder = StateGraph(MessagesState)\n", "builder.add_node(\"tool_calling_llm\", tool_calling_llm)\n", "builder.add_edge(START, \"tool_calling_llm\")\n", "builder.add_edge(\"tool_calling_llm\", END)\n", "graph = builder.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "e8909771-7786-47d6-a53d-6bbc3b365737", "metadata": {}, "source": ["If we pass in `Hello!`, the LLM responds without any tool calls."]}, {"cell_type": "code", "execution_count": 15, "id": "983e2487-c0a5-40a2-afbc-aa53ff49fefc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hello!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hi there! How can I assist you today?\n"]}], "source": ["messages = graph.invoke({\"messages\": HumanMessage(content=\"Hello!\")})\n", "for m in messages['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "3588688b-efd9-4dbc-abf2-7903e3ef89ba", "metadata": {}, "source": ["The LLM chooses to use a tool when it determines that the input or task requires the functionality provided by that tool."]}, {"cell_type": "code", "execution_count": 16, "id": "7fe8b042-ecc8-426f-995e-cc1bbaf7cacc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Multiply 2 and 3!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  multiply (call_Er4gChFoSGzU7lsuaGzfSGTQ)\n", " Call ID: call_Er4gChFoSGzU7lsuaGzfSGTQ\n", "  Args:\n", "    a: 2\n", "    b: 3\n"]}], "source": ["messages = graph.invoke({\"messages\": HumanMessage(content=\"Multiply 2 and 3\")})\n", "for m in messages['messages']:\n", "    m.pretty_print()"]}, {"cell_type": "code", "execution_count": null, "id": "311fbae3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lc-academy-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}