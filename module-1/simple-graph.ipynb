{"cells": [{"cell_type": "markdown", "id": "8d5f3703", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-1/simple-graph.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58238187-lesson-2-simple-graph)"]}, {"attachments": {}, "cell_type": "markdown", "id": "50fa7f8a-8764-4bb9-9968-48b681a0e4f1", "metadata": {}, "source": ["# The Simplest Graph\n", "\n", "Let's build a simple graph with 3 nodes and one conditional edge. \n", "\n", "![Screenshot 2024-08-20 at 3.11.22 PM.png](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66dba5f465f6e9a2482ad935_simple-graph1.png)"]}, {"cell_type": "code", "execution_count": null, "id": "ff151ef1-fa30-482a-94da-8f49964afbc3", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph"]}, {"cell_type": "markdown", "id": "5999f8d0-989f-4638-8ade-5c257cbadfe8", "metadata": {}, "source": ["## State\n", "\n", "First, define the [State](https://langchain-ai.github.io/langgraph/concepts/low_level/#state) of the graph. \n", "\n", "The State schema serves as the input schema for all Nodes and Edges in the graph.\n", "\n", "Let's use the `TypedDict` class from python's `typing` module as our schema, which provides type hints for the keys."]}, {"cell_type": "code", "execution_count": 2, "id": "6a90709b-ddfa-4671-8acc-c59969a29991", "metadata": {}, "outputs": [], "source": ["from typing_extensions import TypedDict\n", "\n", "class State(TypedDict):\n", "    graph_state: str"]}, {"cell_type": "markdown", "id": "888509e1-cbde-4c03-99a0-2560dd2e262d", "metadata": {}, "source": ["## Nodes\n", "\n", "[Nodes](https://langchain-ai.github.io/langgraph/concepts/low_level/#nodes) are just python functions.\n", "\n", "The first positional argument is the state, as defined above.\n", "\n", "Because the state is a `TypedDict` with schema as defined above, each node can access the key, `graph_state`, with `state['graph_state']`.\n", "\n", "Each node returns a new value of the state key `graph_state`.\n", "  \n", "By default, the new value returned by each node [will override](https://langchain-ai.github.io/langgraph/concepts/low_level/#reducers) the prior state value."]}, {"cell_type": "code", "execution_count": 4, "id": "e8aabcb7-494c-4d35-be08-f81c76d75a6b", "metadata": {}, "outputs": [], "source": ["def node_1(state):\n", "    print(\"---Node 1---\")\n", "    return {\"graph_state\": state['graph_state'] +\" I am\"}\n", "\n", "def node_2(state):\n", "    print(\"---Node 2---\")\n", "    return {\"graph_state\": state['graph_state'] +\" happy!\"}\n", "\n", "def node_3(state):\n", "    print(\"---Node 3---\")\n", "    return {\"graph_state\": state['graph_state'] +\" sad!\"}"]}, {"cell_type": "markdown", "id": "ad056608-8c8f-4999-bb53-10583efa4ed8", "metadata": {}, "source": ["## Edges\n", "\n", "[Edges](https://langchain-ai.github.io/langgraph/concepts/low_level/#edges) connect the nodes.\n", "\n", "Normal Edges are used if you want to *always* go from, for example, `node_1` to `node_2`.\n", "\n", "[Conditional Edges](https://langchain-ai.github.io/langgraph/concepts/low_level/#conditional-edges) are used if you want to *optionally* route between nodes.\n", " \n", "Conditional edges are implemented as functions that return the next node to visit based upon some logic."]}, {"cell_type": "code", "execution_count": 5, "id": "7e53543a-902a-4d41-ad3d-25eee260e819", "metadata": {}, "outputs": [], "source": ["import random\n", "from typing import Literal\n", "\n", "def decide_mood(state) -> Literal[\"node_2\", \"node_3\"]:\n", "    \n", "    # Often, we will use state to decide on the next node to visit\n", "    user_input = state['graph_state'] \n", "    \n", "    # Here, let's just do a 50 / 50 split between nodes 2, 3\n", "    if random.random() < 0.5:\n", "\n", "        # 50% of the time, we return Node 2\n", "        return \"node_2\"\n", "    \n", "    # 50% of the time, we return Node 3\n", "    return \"node_3\""]}, {"cell_type": "markdown", "id": "9282ea7a-5ed2-4641-bed8-c3472d54c951", "metadata": {}, "source": ["## Graph Construction\n", "\n", "Now, we build the graph from our [components](\n", "https://langchain-ai.github.io/langgraph/concepts/low_level/) defined above.\n", "\n", "The [StateGraph class](https://langchain-ai.github.io/langgraph/concepts/low_level/#stategraph) is the graph class that we can use.\n", " \n", "First, we initialize a StateGraph with the `State` class we defined above.\n", " \n", "Then, we add our nodes and edges.\n", "\n", "We use the [`START` Node, a special node](https://langchain-ai.github.io/langgraph/concepts/low_level/#start-node) that sends user input to the graph, to indicate where to start our graph.\n", " \n", "The [`END` Node](https://langchain-ai.github.io/langgraph/concepts/low_level/#end-node) is a special node that represents a terminal node. \n", "\n", "Finally, we [compile our graph](https://langchain-ai.github.io/langgraph/concepts/low_level/#compiling-your-graph) to perform a few basic checks on the graph structure. \n", "\n", "We can visualize the graph as a [Mermaid diagram](https://github.com/mermaid-js/mermaid)."]}, {"cell_type": "code", "execution_count": 6, "id": "7deb0359-55c1-4545-b52e-8252994befbb", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "# Build graph\n", "builder = StateGraph(State)\n", "builder.add_node(\"node_1\", node_1)\n", "builder.add_node(\"node_2\", node_2)\n", "builder.add_node(\"node_3\", node_3)\n", "\n", "# Logic\n", "builder.add_edge(START, \"node_1\")\n", "builder.add_conditional_edges(\"node_1\", decide_mood)\n", "builder.add_edge(\"node_2\", END)\n", "builder.add_edge(\"node_3\", END)\n", "\n", "# Add\n", "graph = builder.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "00617c74-2647-44ea-8a2e-310dd96c0d26", "metadata": {}, "source": ["## Graph Invocation\n", "\n", "The compiled graph implements the [runnable](https://python.langchain.com/docs/concepts/runnables/) protocol.\n", "\n", "This provides a standard way to execute LangChain components. \n", " \n", "`invoke` is one of the standard methods in this interface.\n", "\n", "The input is a dictionary `{\"graph_state\": \"Hi, this is lance.\"}`, which sets the initial value for our graph state dict.\n", "\n", "When `invoke` is called, the graph starts execution from the `START` node.\n", "\n", "It progresses through the defined nodes (`node_1`, `node_2`, `node_3`) in order.\n", "\n", "The conditional edge will traverse from node `1` to node `2` or `3` using a 50/50 decision rule. \n", "\n", "Each node function receives the current state and returns a new value, which overrides the graph state.\n", "\n", "The execution continues until it reaches the `END` node."]}, {"cell_type": "code", "execution_count": 7, "id": "e895f17a-e835-4e8a-8e1b-63fe6d27cc52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1---\n", "---Node 3---\n"]}, {"data": {"text/plain": ["{'graph_state': '<PERSON>, this is <PERSON>. I am sad!'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"graph_state\" : \"Hi, this is <PERSON>.\"})"]}, {"cell_type": "markdown", "id": "082399c3-18bd-4b67-97c1-2005f268abc5", "metadata": {}, "source": ["`invoke` runs the entire graph synchronously.\n", "\n", "This waits for each step to complete before moving to the next.\n", "\n", "It returns the final state of the graph after all nodes have executed.\n", "\n", "In this case, it returns the state after `node_3` has completed: \n", "\n", "```\n", "{'graph_state': '<PERSON>, this is <PERSON>. I am sad!'}\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "db16ab8d-b817-4f3a-befc-a02b579c4fca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}