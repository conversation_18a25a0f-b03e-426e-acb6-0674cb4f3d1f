{"cells": [{"cell_type": "markdown", "id": "cf7ccb32", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-2/chatbot-external-memory.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239440-lesson-6-chatbot-w-summarizing-messages-and-external-memory)"]}, {"cell_type": "markdown", "id": "af6c7afe-1037-41ab-98e4-494692e47402", "metadata": {}, "source": ["# Chatbot with message summarization & external DB memory\n", "\n", "## Review\n", "\n", "We've covered how to customize graph state schema and reducer. \n", " \n", "We've also shown a number of tricks for trimming or filtering messages in graph state. \n", "\n", "We've used these concepts in a Chatbot with memory that produces a running summary of the conversation.\n", "\n", "## Goals\n", "\n", "But, what if we want our <PERSON>t<PERSON> to have memory that persists indefinitely?\n", "\n", "Now, we'll introduce some more advanced checkpointers that support external databases. \n", "\n", "Here, we'll show how to use [Sqlite as a checkpointer](https://langchain-ai.github.io/langgraph/concepts/low_level/#checkpointer), but other checkpointers, such as [Postgres](https://langchain-ai.github.io/langgraph/how-tos/persistence_postgres/) are available!"]}, {"cell_type": "code", "execution_count": 5, "id": "85ed78d9-6ca2-45ac-96a9-52e341ec519d", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph-checkpoint-sqlite langchain_core langgraph langchain_openai"]}, {"cell_type": "code", "execution_count": null, "id": "2e10c4d4", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "b40d25c0-e9b5-4854-bf07-3cc3ff07122e", "metadata": {}, "source": ["## Sqlite\n", "\n", "A good starting point here is the [SqliteSaver checkpointer](https://langchain-ai.github.io/langgraph/concepts/low_level/#checkpointer).\n", "\n", "Sqlite is a [small, fast, highly popular](https://x.com/karpathy/status/1819490455664685297) SQL database. \n", " \n", "If we supply `\":memory:\"` it creates an in-memory Sqlite database."]}, {"cell_type": "code", "execution_count": null, "id": "fae15402-17ae-4e89-8ecf-4c89e08b22fe", "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "# In memory\n", "conn = sqlite3.connect(\":memory:\", check_same_thread = False)"]}, {"cell_type": "markdown", "id": "c2bf53ec-6d4a-42ce-8183-344795eed403", "metadata": {}, "source": ["But, if we supply a db path, then it will create a database for us!"]}, {"cell_type": "code", "execution_count": 2, "id": "58339167-920c-4994-a0a7-0a9c5d4f7cf7", "metadata": {}, "outputs": [], "source": ["# pull file if it doesn't exist and connect to local db\n", "!mkdir -p state_db && [ ! -f state_db/example.db ] && wget -P state_db https://github.com/langchain-ai/langchain-academy/raw/main/module-2/state_db/example.db\n", "\n", "db_path = \"state_db/example.db\"\n", "conn = sqlite3.connect(db_path, check_same_thread=False)"]}, {"cell_type": "code", "execution_count": 3, "id": "3c7736b6-a750-48f8-a838-8e7616b12250", "metadata": {}, "outputs": [], "source": ["# Here is our checkpointer \n", "from langgraph.checkpoint.sqlite import SqliteSaver\n", "memory = SqliteSaver(conn)"]}, {"cell_type": "markdown", "id": "9d8cb629-213f-4b87-965e-19b812c42da1", "metadata": {}, "source": ["Let's re-define our chatbot."]}, {"cell_type": "code", "execution_count": 4, "id": "dc414e29-2078-41a0-887c-af1a6a3d72c0", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_core.messages import SystemMessage, HumanMessage, RemoveMessage\n", "\n", "from langgraph.graph import END\n", "from langgraph.graph import MessagesState\n", "\n", "model = ChatOpenAI(model=\"gpt-4o\",temperature=0)\n", "\n", "class State(MessagesState):\n", "    summary: str\n", "\n", "# Define the logic to call the model\n", "def call_model(state: State):\n", "    \n", "    # Get summary if it exists\n", "    summary = state.get(\"summary\", \"\")\n", "\n", "    # If there is summary, then we add it\n", "    if summary:\n", "        \n", "        # Add summary to system message\n", "        system_message = f\"Summary of conversation earlier: {summary}\"\n", "\n", "        # Append summary to any newer messages\n", "        messages = [SystemMessage(content=system_message)] + state[\"messages\"]\n", "    \n", "    else:\n", "        messages = state[\"messages\"]\n", "    \n", "    response = model.invoke(messages)\n", "    return {\"messages\": response}\n", "\n", "def summarize_conversation(state: State):\n", "    \n", "    # First, we get any existing summary\n", "    summary = state.get(\"summary\", \"\")\n", "\n", "    # Create our summarization prompt \n", "    if summary:\n", "        \n", "        # A summary already exists\n", "        summary_message = (\n", "            f\"This is summary of the conversation to date: {summary}\\n\\n\"\n", "            \"Extend the summary by taking into account the new messages above:\"\n", "        )\n", "        \n", "    else:\n", "        summary_message = \"Create a summary of the conversation above:\"\n", "\n", "    # Add prompt to our history\n", "    messages = state[\"messages\"] + [HumanMessage(content=summary_message)]\n", "    response = model.invoke(messages)\n", "    \n", "    # Delete all but the 2 most recent messages\n", "    delete_messages = [RemoveMessage(id=m.id) for m in state[\"messages\"][:-2]]\n", "    return {\"summary\": response.content, \"messages\": delete_messages}\n", "\n", "# Determine whether to end or summarize the conversation\n", "def should_continue(state: State):\n", "    \n", "    \"\"\"Return the next node to execute.\"\"\"\n", "    \n", "    messages = state[\"messages\"]\n", "    \n", "    # If there are more than six messages, then we summarize the conversation\n", "    if len(messages) > 6:\n", "        return \"summarize_conversation\"\n", "    \n", "    # Otherwise we can just end\n", "    return END"]}, {"cell_type": "markdown", "id": "41c13c0b-a383-4f73-9cc1-63f0eed8f190", "metadata": {}, "source": ["Now, we just re-compile with our sqlite checkpointer."]}, {"cell_type": "code", "execution_count": 5, "id": "e867fd95-91eb-4ce1-82fc-bb72d611a96d", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "from langgraph.graph import StateGraph, START\n", "\n", "# Define a new graph\n", "workflow = StateGraph(State)\n", "workflow.add_node(\"conversation\", call_model)\n", "workflow.add_node(summarize_conversation)\n", "\n", "# Set the entrypoint as conversation\n", "workflow.add_edge(START, \"conversation\")\n", "workflow.add_conditional_edges(\"conversation\", should_continue)\n", "workflow.add_edge(\"summarize_conversation\", END)\n", "\n", "# Compile\n", "graph = workflow.compile(checkpointer=memory)\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "8769db99-3938-45e6-a594-56beb18d6c45", "metadata": {}, "source": ["Now, we can invoke the graph several times. "]}, {"cell_type": "code", "execution_count": 6, "id": "0f4094a0-d240-4be8-903a-7d9f605bdc5c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/l9/bpjxdmfx7lvd1fbdjn38y5dh0000gn/T/ipykernel_18873/2173919996.py:55: Lang<PERSON>hainBetaWarning: The class `RemoveMessage` is in beta. It is actively being worked on, so the API may change.\n", "  delete_messages = [RemoveMessage(id=m.id) for m in state[\"messages\"][:-2]]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Hello again, <PERSON>! It's great to hear from you. Since you like the 49ers, is there a particular player or moment in their history that stands out to you? Or perhaps you'd like to discuss their current season? Let me know!\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "Your name is <PERSON>! How can I assist you today? Would you like to talk more about the San Francisco 49ers or something else?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "That's awesome, <PERSON>! The San Francisco 49ers have a rich history and a passionate fan base. Is there a specific aspect of the team you'd like to discuss? For example, we could talk about:\n", "\n", "- Their legendary players like <PERSON> and <PERSON>\n", "- Memorable games and Super Bowl victories\n", "- The current roster and season prospects\n", "- Rivalries, like the one with the Seattle Seahawks\n", "- Levi's Stadium and the fan experience\n", "\n", "Let me know what interests you!\n"]}], "source": ["# Create a thread\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Start conversation\n", "input_message = HumanMessage(content=\"hi! I'm <PERSON>\")\n", "output = graph.invoke({\"messages\": [input_message]}, config) \n", "for m in output['messages'][-1:]:\n", "    m.pretty_print()\n", "\n", "input_message = HumanMessage(content=\"what's my name?\")\n", "output = graph.invoke({\"messages\": [input_message]}, config) \n", "for m in output['messages'][-1:]:\n", "    m.pretty_print()\n", "\n", "input_message = HumanMessage(content=\"i like the 49ers!\")\n", "output = graph.invoke({\"messages\": [input_message]}, config) \n", "for m in output['messages'][-1:]:\n", "    m.pretty_print()"]}, {"cell_type": "markdown", "id": "c0f3e842-4497-45e2-a924-69672a9bcb33", "metadata": {}, "source": ["Let's confirm that our state is saved locally."]}, {"cell_type": "code", "execution_count": 7, "id": "d2ab158a-5a82-417a-8841-730a4cc18ea7", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'messages': [HumanMessage(content=\"hi! I'm <PERSON>\", id='d5bb4b3f-b1e9-4f61-8c75-7a7210b30253'), AIMessage(content=\"Hello again, <PERSON>! It's great to hear from you. Since you like the 49ers, is there a particular player or moment in their history that stands out to you? Or perhaps you'd like to discuss their current season? Let me know!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 50, 'prompt_tokens': 337, 'total_tokens': 387}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5', 'finish_reason': 'stop', 'logprobs': None}, id='run-dde04d51-d305-4a9e-8ad5-6bdf5583196e-0', usage_metadata={'input_tokens': 337, 'output_tokens': 50, 'total_tokens': 387}), HumanMessage(content=\"what's my name?\", id='d7530770-f130-4a05-a602-a96fd87859c6'), AIMessage(content='Your name is Lance! How can I assist you today? Would you like to talk more about the San Francisco 49ers or something else?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 29, 'prompt_tokens': 243, 'total_tokens': 272}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-763b6387-c4c9-4658-9d01-7c018dde7a62-0', usage_metadata={'input_tokens': 243, 'output_tokens': 29, 'total_tokens': 272}), HumanMessage(content='i like the 49ers!', id='235dcec4-b656-4bee-b741-e330e1a026e2'), AIMessage(content=\"That's awesome, Lance! The San Francisco 49ers have a rich history and a passionate fan base. Is there a specific aspect of the team you'd like to discuss? For example, we could talk about:\\n\\n- Their legendary players like Joe Montana and Jerry Rice\\n- Memorable games and Super Bowl victories\\n- The current roster and season prospects\\n- Rivalries, like the one with the Seattle Seahawks\\n- Levi's Stadium and the fan experience\\n\\nLet me know what interests you!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 98, 'prompt_tokens': 287, 'total_tokens': 385}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-729860b2-16c3-46ff-ad1e-0a5475565b20-0', usage_metadata={'input_tokens': 287, 'output_tokens': 98, 'total_tokens': 385})], 'summary': 'Here\\'s an extended summary of the conversation:\\n\\nLance introduced himself multiple times during the conversation, each time stating, \"Hi! I\\'m Lance.\" He expressed his fondness for the San Francisco 49ers football team. The AI assistant acknowledged Lance\\'s name each time and showed willingness to discuss the 49ers, offering to talk about various aspects of the team such as their history, current roster, memorable games, prospects for the upcoming season, rivalries, and their home stadium, Levi\\'s Stadium. Despite the AI\\'s attempts to engage in a more detailed discussion about the 49ers, Lance reintroduced himself again without directly responding to the AI\\'s questions or prompts about the team. The conversation remained brief and somewhat repetitive, focusing mainly on Lance\\'s introductions and his interest in the 49ers.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1ef6a36d-ca9c-6144-801b-6d0cf97adc73'}}, metadata={'source': 'loop', 'writes': {'conversation': {'messages': AIMessage(content=\"That's awesome, Lance! The San Francisco 49ers have a rich history and a passionate fan base. Is there a specific aspect of the team you'd like to discuss? For example, we could talk about:\\n\\n- Their legendary players like Joe Montana and Jerry Rice\\n- Memorable games and Super Bowl victories\\n- The current roster and season prospects\\n- Rivalries, like the one with the Seattle Seahawks\\n- Levi's Stadium and the fan experience\\n\\nLet me know what interests you!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 98, 'prompt_tokens': 287, 'total_tokens': 385}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-729860b2-16c3-46ff-ad1e-0a5475565b20-0', usage_metadata={'input_tokens': 287, 'output_tokens': 98, 'total_tokens': 385})}}, 'step': 27, 'parents': {}}, created_at='2024-09-03T20:55:33.466540+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1ef6a36d-b8f3-6b40-801a-494776d2e9e0'}}, tasks=())"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "graph_state = graph.get_state(config)\n", "graph_state"]}, {"cell_type": "markdown", "id": "1e21152d-ed9c-408d-b7d5-f634c9ce81e2", "metadata": {}, "source": ["### Persisting state\n", "\n", "Using database like Sqlite means state is persisted! \n", "\n", "For example, we can re-start the notebook kernel and see that we can still load from Sqlite DB on disk.\n"]}, {"cell_type": "code", "execution_count": 8, "id": "b9a44dc5-be04-45fa-a6fc-27b0f8ee4678", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'messages': [HumanMessage(content=\"hi! I'm <PERSON>\", id='d5bb4b3f-b1e9-4f61-8c75-7a7210b30253'), AIMessage(content=\"Hello again, <PERSON>! It's great to hear from you. Since you like the 49ers, is there a particular player or moment in their history that stands out to you? Or perhaps you'd like to discuss their current season? Let me know!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 50, 'prompt_tokens': 337, 'total_tokens': 387}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_157b3831f5', 'finish_reason': 'stop', 'logprobs': None}, id='run-dde04d51-d305-4a9e-8ad5-6bdf5583196e-0', usage_metadata={'input_tokens': 337, 'output_tokens': 50, 'total_tokens': 387}), HumanMessage(content=\"what's my name?\", id='d7530770-f130-4a05-a602-a96fd87859c6'), AIMessage(content='Your name is Lance! How can I assist you today? Would you like to talk more about the San Francisco 49ers or something else?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 29, 'prompt_tokens': 243, 'total_tokens': 272}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-763b6387-c4c9-4658-9d01-7c018dde7a62-0', usage_metadata={'input_tokens': 243, 'output_tokens': 29, 'total_tokens': 272}), HumanMessage(content='i like the 49ers!', id='235dcec4-b656-4bee-b741-e330e1a026e2'), AIMessage(content=\"That's awesome, Lance! The San Francisco 49ers have a rich history and a passionate fan base. Is there a specific aspect of the team you'd like to discuss? For example, we could talk about:\\n\\n- Their legendary players like Joe Montana and Jerry Rice\\n- Memorable games and Super Bowl victories\\n- The current roster and season prospects\\n- Rivalries, like the one with the Seattle Seahawks\\n- Levi's Stadium and the fan experience\\n\\nLet me know what interests you!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 98, 'prompt_tokens': 287, 'total_tokens': 385}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-729860b2-16c3-46ff-ad1e-0a5475565b20-0', usage_metadata={'input_tokens': 287, 'output_tokens': 98, 'total_tokens': 385})], 'summary': 'Here\\'s an extended summary of the conversation:\\n\\nLance introduced himself multiple times during the conversation, each time stating, \"Hi! I\\'m Lance.\" He expressed his fondness for the San Francisco 49ers football team. The AI assistant acknowledged Lance\\'s name each time and showed willingness to discuss the 49ers, offering to talk about various aspects of the team such as their history, current roster, memorable games, prospects for the upcoming season, rivalries, and their home stadium, Levi\\'s Stadium. Despite the AI\\'s attempts to engage in a more detailed discussion about the 49ers, Lance reintroduced himself again without directly responding to the AI\\'s questions or prompts about the team. The conversation remained brief and somewhat repetitive, focusing mainly on Lance\\'s introductions and his interest in the 49ers.'}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1ef6a36d-ca9c-6144-801b-6d0cf97adc73'}}, metadata={'source': 'loop', 'writes': {'conversation': {'messages': AIMessage(content=\"That's awesome, Lance! The San Francisco 49ers have a rich history and a passionate fan base. Is there a specific aspect of the team you'd like to discuss? For example, we could talk about:\\n\\n- Their legendary players like Joe Montana and Jerry Rice\\n- Memorable games and Super Bowl victories\\n- The current roster and season prospects\\n- Rivalries, like the one with the Seattle Seahawks\\n- Levi's Stadium and the fan experience\\n\\nLet me know what interests you!\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 98, 'prompt_tokens': 287, 'total_tokens': 385}, 'model_name': 'gpt-4o-2024-05-13', 'system_fingerprint': 'fp_fde2829a40', 'finish_reason': 'stop', 'logprobs': None}, id='run-729860b2-16c3-46ff-ad1e-0a5475565b20-0', usage_metadata={'input_tokens': 287, 'output_tokens': 98, 'total_tokens': 385})}}, 'step': 27, 'parents': {}}, created_at='2024-09-03T20:55:33.466540+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1ef6a36d-b8f3-6b40-801a-494776d2e9e0'}}, tasks=())"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a thread\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "graph_state = graph.get_state(config)\n", "graph_state"]}, {"cell_type": "markdown", "id": "8466e418-1a46-4cdb-a51a-6ae14281bb85", "metadata": {}, "source": ["## LangGraph Studio\n", "\n", "**⚠️ DISCLAIMER**\n", "\n", "Since the filming of these videos, we've updated Studio so that it can be run locally and opened in your browser. This is now the preferred way to run Studio (rather than using the Desktop App as shown in the video). See documentation [here](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#local-development-server) on the local development server and [here](https://langchain-ai.github.io/langgraph/how-tos/local-studio/#run-the-development-server). To start the local development server, run the following command in your terminal in the `/studio` directory in this module:\n", "\n", "```\n", "langgraph dev\n", "```\n", "\n", "You should see the following output:\n", "```\n", "- 🚀 API: http://127.0.0.1:2024\n", "- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\n", "- 📚 API Docs: http://127.0.0.1:2024/docs\n", "```\n", "\n", "Open your browser and navigate to the Studio UI: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`."]}, {"cell_type": "markdown", "id": "c4916d8b", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}