{"cells": [{"cell_type": "markdown", "id": "e2996fea", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-2/multiple-schemas.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239434-lesson-3-multiple-schemas)"]}, {"cell_type": "markdown", "id": "693d9912-8d56-46a2-a445-3ee5651fe433", "metadata": {}, "source": ["# Multiple Schemas\n", "\n", "## Review\n", "\n", "We just covered state schema and reducers.\n", "\n", "Typically, all graph nodes communicate with a single schema. \n", "\n", "Also, this single schema contains the graph's input and output keys / channels.\n", "\n", "## Goals\n", "\n", "But, there are cases where we may want a bit more control over this:\n", "\n", "* Internal nodes may pass information that is *not required* in the graph's input / output.\n", "\n", "* We may also want to use different input / output schemas for the graph. The output might, for example, only contain a single relevant output key.\n", "\n", "We'll discuss a few ways to customize graphs with multiple schemas."]}, {"cell_type": "code", "execution_count": 1, "id": "4d727cc2-5a43-4eb5-9d69-82bbbcc35bd9", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph"]}, {"cell_type": "markdown", "id": "29b3d109-6bf2-4271-9775-556ee4bd900d", "metadata": {}, "source": ["## Private State\n", "\n", "First, let's cover the case of passing [private state](https://langchain-ai.github.io/langgraph/how-tos/pass_private_state/) between nodes.\n", "\n", "This is useful for anything needed as part of the intermediate working logic of the graph, but not relevant for the overall graph input or output.\n", "\n", "We'll define an `OverallState` and a `PrivateState`.\n", "\n", "`node_2` uses `PrivateState` as input, but writes out to `OverallState`."]}, {"cell_type": "code", "execution_count": 2, "id": "038ca2e4-7d6d-49d5-b213-b38469cde434", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict\n", "from IPython.display import Image, display\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "class OverallState(TypedDict):\n", "    foo: int\n", "\n", "class PrivateState(TypedDict):\n", "    baz: int\n", "\n", "def node_1(state: OverallState) -> PrivateState:\n", "    print(\"---Node 1---\")\n", "    return {\"baz\": state['foo'] + 1}\n", "\n", "def node_2(state: PrivateState) -> OverallState:\n", "    print(\"---Node 2---\")\n", "    return {\"foo\": state['baz'] + 1}\n", "\n", "# Build graph\n", "builder = StateGraph(OverallState)\n", "builder.add_node(\"node_1\", node_1)\n", "builder.add_node(\"node_2\", node_2)\n", "\n", "# Logic\n", "builder.add_edge(START, \"node_1\")\n", "builder.add_edge(\"node_1\", \"node_2\")\n", "builder.add_edge(\"node_2\", END)\n", "\n", "# Add\n", "graph = builder.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 3, "id": "3dc9cd64-4bd3-4c0a-8f8f-d58c551428e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Node 1---\n", "---Node 2---\n"]}, {"data": {"text/plain": ["{'foo': 3}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"foo\" : 1})"]}, {"cell_type": "markdown", "id": "50a29f37-f653-4a56-ad0a-345d7f632ea0", "metadata": {}, "source": ["`baz` is only included in `PrivateState`.\n", "\n", "`node_2` uses `PrivateState` as input, but writes out to `OverallState`.\n", "\n", "So, we can see that `baz` is excluded from the graph output because it is not in `OverallState`."]}, {"cell_type": "markdown", "id": "75a8362f-009b-4ec2-abe5-8fb318e39966", "metadata": {}, "source": ["## Input / Output Schema\n", "\n", "By default, `StateGraph` takes in a single schema and all nodes are expected to communicate with that schema. \n", "\n", "However, it is also possible to [define explicit input and output schemas for a graph](https://langchain-ai.github.io/langgraph/how-tos/input_output_schema/?h=input+outp).\n", "\n", "Often, in these cases, we define an \"internal\" schema that contains *all* keys relevant to graph operations.\n", "\n", "But, we use specific `input` and `output` schemas to constrain the input and output.\n", "\n", "First, let's just run the graph with a single schema."]}, {"cell_type": "code", "execution_count": 4, "id": "5323068a-907a-438c-8db5-46e5d452ad72", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["class OverallState(TypedDict):\n", "    question: str\n", "    answer: str\n", "    notes: str\n", "\n", "def thinking_node(state: OverallState):\n", "    return {\"answer\": \"bye\", \"notes\": \"... his name is <PERSON>\"}\n", "\n", "def answer_node(state: OverallState):\n", "    return {\"answer\": \"bye <PERSON>\"}\n", "\n", "graph = StateGraph(OverallState)\n", "graph.add_node(\"answer_node\", answer_node)\n", "graph.add_node(\"thinking_node\", thinking_node)\n", "graph.add_edge(START, \"thinking_node\")\n", "graph.add_edge(\"thinking_node\", \"answer_node\")\n", "graph.add_edge(\"answer_node\", END)\n", "\n", "graph = graph.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "853fc90c-bf82-4d51-b3a5-ceb0b0ae5233", "metadata": {}, "source": ["Notice that the output of invoke contains all keys in `OverallState`. "]}, {"cell_type": "code", "execution_count": 5, "id": "507d35e6-f65c-4e89-b26e-a0ef7b90be83", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'question': 'hi', 'answer': 'bye <PERSON>', 'notes': '... his name is <PERSON>'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"question\":\"hi\"})"]}, {"cell_type": "markdown", "id": "e5a899c3-e1b0-48eb-9a36-8c787e378ef0", "metadata": {}, "source": ["Now, let's use a specific `input` and `output` schema with our graph.\n", "\n", "Here, `input` / `output` schemas perform *filtering* on what keys are permitted on the input and output of the graph. \n", "\n", "In addition, we can use a type hint `state: InputState` to specify the input schema of each of our nodes.\n", "\n", "This is important when the graph is using multiple schemas.\n", "\n", "We use type hints below to, for example, show that the output of `answer_node` will be filtered to `OutputState`. "]}, {"cell_type": "code", "execution_count": null, "id": "682b3d10-c78a-41c2-a5ff-842e1688c95f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'question': 'hi', 'answer': 'bye <PERSON>', 'notes': '... his is name is <PERSON>'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["class InputState(TypedDict):\n", "    question: str\n", "\n", "class OutputState(TypedDict):\n", "    answer: str\n", "\n", "class OverallState(TypedDict):\n", "    question: str\n", "    answer: str\n", "    notes: str\n", "\n", "def thinking_node(state: InputState):\n", "    return {\"answer\": \"bye\", \"notes\": \"... his is name is <PERSON>\"}\n", "\n", "def answer_node(state: OverallState) -> OutputState:\n", "    return {\"answer\": \"bye <PERSON>\"}\n", "\n", "graph = StateGraph(OverallState, input_schema=InputState, output_schema=OutputState)\n", "graph.add_node(\"answer_node\", answer_node)\n", "graph.add_node(\"thinking_node\", thinking_node)\n", "graph.add_edge(START, \"thinking_node\")\n", "graph.add_edge(\"thinking_node\", \"answer_node\")\n", "graph.add_edge(\"answer_node\", END)\n", "\n", "graph = graph.compile()\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))\n", "\n", "graph.invoke({\"question\":\"hi\"})"]}, {"cell_type": "markdown", "id": "f1e5ff21", "metadata": {}, "source": ["We can see the `output` schema constrains the output to only the `answer` key."]}, {"cell_type": "markdown", "id": "dad97b85", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "lc-academy-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}