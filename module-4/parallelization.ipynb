{"cells": [{"cell_type": "markdown", "id": "0d0e279f", "metadata": {}, "source": ["[![Open in Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/langchain-ai/langchain-academy/blob/main/module-4/parallelization.ipynb) [![Open in LangChain Academy](https://cdn.prod.website-files.com/65b8cd72835ceeacd4449a53/66e9eba12c7b7688aa3dbb5e_LCA-badge-green.svg)](https://academy.langchain.com/courses/take/intro-to-langgraph/lessons/58239934-lesson-1-parallelization)"]}, {"cell_type": "markdown", "id": "f4169bfb-769a-4db3-833e-c827f19024b2", "metadata": {}, "source": ["# Parallel node execution\n", "\n", "## Review\n", "\n", "In module 3, we went in-depth on `human-in-the loop`, showing 3 common use-cases:\n", "\n", "(1) `Approval` - We can interrupt our agent, surface state to a user, and allow the user to accept an action\n", "\n", "(2) `Debugging` - We can rewind the graph to reproduce or avoid issues\n", "\n", "(3) `Editing` - You can modify the state \n", "\n", "## Goals\n", "\n", "This module will build on `human-in-the-loop` as well as the `memory` concepts discussed in module 2.\n", "\n", "We will dive into `multi-agent` workflows, and build up to a multi-agent research assistant that ties together all of the modules from this course.\n", "\n", "To build this multi-agent research assistant, we'll first discuss a few LangGraph controllability topics.\n", "\n", "We'll start with [parallelization](https://langchain-ai.github.io/langgraph/how-tos/branching/#how-to-create-branches-for-parallel-node-execution).\n", "\n", "## Fan out and fan in\n", "\n", "Let's build a simple linear graph that over-writes the state at each step."]}, {"cell_type": "code", "execution_count": 1, "id": "618eab5c-4ef7-4273-8e0b-a9c847897ed7", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U  langgraph tavily-python wikipedia langchain_openai langchain_community langgraph_sdk"]}, {"cell_type": "code", "execution_count": 2, "id": "31bbec0d", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 3, "id": "1dd77093-1794-4bd7-8c57-58f59a74c20b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "from typing import Any\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "class State(TypedDict):\n", "    # The operator.add reducer fn makes this append-only\n", "    state: str\n", "\n", "class ReturnNodeValue:\n", "    def __init__(self, node_secret: str):\n", "        self._value = node_secret\n", "\n", "    def __call__(self, state: State) -> Any:\n", "        print(f\"Adding {self._value} to {state['state']}\")\n", "        return {\"state\": [self._value]}\n", "\n", "# Add nodes\n", "builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"a\", ReturnNodeValue(\"I'm A\"))\n", "builder.add_node(\"b\", ReturnNodeValue(\"I'm B\"))\n", "builder.add_node(\"c\", ReturnNodeValue(\"I'm C\"))\n", "builder.add_node(\"d\", ReturnNodeValue(\"I'm D\"))\n", "\n", "# Flow\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"b\", \"c\")\n", "builder.add_edge(\"c\", \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "bdd027d3-ef9f-4d43-b190-e9f07d521e18", "metadata": {}, "source": ["We over-write state, as expected."]}, {"cell_type": "code", "execution_count": 4, "id": "bf260088-90d5-45b2-93ab-42f241560840", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding I'm A to []\n", "Adding I'm B to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm <PERSON>\"]\n", "Adding I'm <PERSON> to [\"I'm <PERSON>\"]\n"]}, {"data": {"text/plain": ["{'state': [\"I'm <PERSON>\"]}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"state\": []})"]}, {"cell_type": "markdown", "id": "a9dec27d-dc43-4088-beb2-53ad090d2971", "metadata": {}, "source": ["Now, let's run `b` and `c` in parallel. \n", "\n", "And then run `d`.\n", "\n", "We can do this easily with fan-out from `a` to `b` and `c`, and then fan-in to `d`.\n", "\n", "The the state updates are applied at the end of each step.\n", "\n", "Let's run it."]}, {"cell_type": "code", "execution_count": 5, "id": "8fdeaaab-a8c3-470f-89ef-9cf0a2760667", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"a\", ReturnNodeValue(\"I'm A\"))\n", "builder.add_node(\"b\", ReturnNodeValue(\"I'm B\"))\n", "builder.add_node(\"c\", ReturnNodeValue(\"I'm C\"))\n", "builder.add_node(\"d\", ReturnNodeValue(\"I'm D\"))\n", "\n", "# Flow\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"a\", \"c\")\n", "builder.add_edge(\"b\", \"d\")\n", "builder.add_edge(\"c\", \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "35238fde-0230-4ae8-9200-158a8835c4f1", "metadata": {}, "source": ["**We see an error**! \n", "\n", "This is because both `b` and `c` are writing to the same state key / channel in the same step. "]}, {"cell_type": "code", "execution_count": 6, "id": "9048b041-6849-4f09-9811-6b7a80f67859", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding I'm A to []\n", "Adding I'm B to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm A\"]\n", "An error occurred: At key 'state': Can receive only one value per step. Use an Annotated key to handle multiple values.\n", "For troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE\n"]}], "source": ["from langgraph.errors import InvalidUpdateError\n", "try:\n", "    graph.invoke({\"state\": []})\n", "except InvalidUpdateError as e:\n", "    print(f\"An error occurred: {e}\")"]}, {"cell_type": "markdown", "id": "64cc329d-59fa-4c26-adcf-9122a824955d", "metadata": {}, "source": ["When using fan out, we need to be sure that we are using a reducer if steps are writing to the same the channel / key. \n", "\n", "As we touched on in Module 2, `operator.add` is a function from Python's built-in operator module.\n", "\n", "When `operator.add` is applied to lists, it performs list concatenation."]}, {"cell_type": "code", "execution_count": 7, "id": "8f1292ac-510a-4801-b2a3-e2c6d2d9582a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import operator\n", "from typing import Annotated\n", "\n", "class State(TypedDict):\n", "    # The operator.add reducer fn makes this append-only\n", "    state: Annotated[list, operator.add]\n", "\n", "# Add nodes\n", "builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"a\", ReturnNodeValue(\"I'm A\"))\n", "builder.add_node(\"b\", ReturnNodeValue(\"I'm B\"))\n", "builder.add_node(\"c\", ReturnNodeValue(\"I'm C\"))\n", "builder.add_node(\"d\", ReturnNodeValue(\"I'm D\"))\n", "\n", "# Flow\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"a\", \"c\")\n", "builder.add_edge(\"b\", \"d\")\n", "builder.add_edge(\"c\", \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 8, "id": "ffbad231-fc1d-49b1-a9fc-ed9153fa3977", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding I'm A to []\n", "Adding I'm B to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm <PERSON>\", \"I'm <PERSON>\", \"I'm <PERSON>\"]\n"]}, {"data": {"text/plain": ["{'state': [\"I'm <PERSON>\", \"I'm <PERSON>\", \"I'm <PERSON>\", \"I'm <PERSON>\"]}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"state\": []})"]}, {"cell_type": "markdown", "id": "bdf5baa2-cecd-44b6-b0c4-d258340783f8", "metadata": {}, "source": ["Now we see that we append to state for the updates made in parallel by `b` and `c`."]}, {"cell_type": "markdown", "id": "ed6fc7c7-198d-41be-867f-e77c93ba3217", "metadata": {}, "source": ["## Waiting for nodes to finish\n", "\n", "Now, lets consider a case where one parallel path has more steps than the other one."]}, {"cell_type": "code", "execution_count": 9, "id": "f50b5d4f-dd39-4c22-b623-e0abc23f9144", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"a\", ReturnNodeValue(\"I'm A\"))\n", "builder.add_node(\"b\", ReturnNodeValue(\"I'm B\"))\n", "builder.add_node(\"b2\", ReturnNodeValue(\"I'm B2\"))\n", "builder.add_node(\"c\", ReturnNodeValue(\"I'm C\"))\n", "builder.add_node(\"d\", ReturnNodeValue(\"I'm D\"))\n", "\n", "# Flow\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"a\", \"c\")\n", "builder.add_edge(\"b\", \"b2\")\n", "builder.add_edge([\"b2\", \"c\"], \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "id": "11640e6f-ac62-4ad4-89d9-7f6f9b56bf7a", "metadata": {}, "source": ["In this case, `b`, `b2`, and `c` are all part of the same step.\n", "\n", "The graph will wait for all of these to be completed before proceeding to step `d`. "]}, {"cell_type": "code", "execution_count": 10, "id": "fafda930-e75b-410f-ba93-eb5fc0219303", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding I'm A to []\n", "Adding I'm B to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm A\"]\n", "Adding I'm B2 to [\"I'm A\", \"I'm B\", \"I'm <PERSON>\"]\n", "Adding I'm D to [\"I'm A\", \"I'm B\", \"I'm C\", \"I'm B2\"]\n"]}, {"data": {"text/plain": ["{'state': [\"I'm <PERSON>\", \"I'm B\", \"I'm C\", \"I'm B2\", \"I'm D\"]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"state\": []})"]}, {"cell_type": "markdown", "id": "6610a2e3-b053-47e8-bf4e-0968dfaa0a5d", "metadata": {}, "source": ["## Setting the order of state updates\n", "\n", "However, within each step we don't have specific control over the order of the state updates!\n", "\n", "In simple terms, it is a deterministic order determined by <PERSON>Graph based upon graph topology that **we do not control**. \n", "\n", "Above, we see that `c` is added before `b2`.\n", "\n", "However, we can use a custom reducer to customize this e.g., sort state updates."]}, {"cell_type": "code", "execution_count": 11, "id": "24788e73-0950-432e-ad32-7987ea076529", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def sorting_reducer(left, right):\n", "    \"\"\" Combines and sorts the values in a list\"\"\"\n", "    if not isinstance(left, list):\n", "        left = [left]\n", "\n", "    if not isinstance(right, list):\n", "        right = [right]\n", "    \n", "    return sorted(left + right, reverse=False)\n", "\n", "class State(TypedDict):\n", "    # sorting_reducer will sort the values in state\n", "    state: Annotated[list, sorting_reducer]\n", "\n", "# Add nodes\n", "builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"a\", ReturnNodeValue(\"I'm A\"))\n", "builder.add_node(\"b\", ReturnNodeValue(\"I'm B\"))\n", "builder.add_node(\"b2\", ReturnNodeValue(\"I'm B2\"))\n", "builder.add_node(\"c\", ReturnNodeValue(\"I'm C\"))\n", "builder.add_node(\"d\", ReturnNodeValue(\"I'm D\"))\n", "\n", "# Flow\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"a\", \"c\")\n", "builder.add_edge(\"b\", \"b2\")\n", "builder.add_edge([\"b2\", \"c\"], \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 12, "id": "607dba2e-f9f0-4bc7-8ba6-684521a49bdc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding I'm A to []\n", "Adding I'm B to [\"I'm A\"]\n", "Adding I'm <PERSON> to [\"I'm A\"]\n", "Adding I'm B2 to [\"I'm A\", \"I'm B\", \"I'm <PERSON>\"]\n", "Adding I'm D to [\"I'm A\", \"I'm B\", \"I'm B2\", \"I'm C\"]\n"]}, {"data": {"text/plain": ["{'state': [\"I'm A\", \"I'm B\", \"I'm B2\", \"I'm <PERSON>\", \"I'm D\"]}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"state\": []})"]}, {"cell_type": "markdown", "id": "fb1714c0-e881-48e7-bcb8-a60016f0485e", "metadata": {}, "source": ["Now, the reducer sorts the updated state values!\n", "\n", "The `sorting_reducer` example sorts all values globally. We can also: \n", "\n", "1. Write outputs to a separate field in the state during the parallel step\n", "2. Use a \"sink\" node after the parallel step to combine and order those outputs\n", "3. Clear the temporary field after combining\n", "\n", "See the [docs](https://langchain-ai.github.io/langgraph/how-tos/branching/#stable-sorting) for more details.\n"]}, {"cell_type": "markdown", "id": "34e0750b-e6af-40d9-835c-c664da5a2d3b", "metadata": {}, "source": ["## Working with LLMs\n", "\n", "Now, lets add a realistic example! \n", "\n", "We want to gather context from two external sources (Wikipedia and Web-Search) and have an LLM answer a question."]}, {"cell_type": "code", "execution_count": 13, "id": "e1e9d03c-cb41-415c-862d-c9616d5a2d07", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(model=\"gpt-4o\", temperature=0) "]}, {"cell_type": "code", "execution_count": 14, "id": "0f75cc78-d1a1-47a5-8648-bf5a79c883de", "metadata": {}, "outputs": [], "source": ["class State(TypedDict):\n", "    question: str\n", "    answer: str\n", "    context: Annotated[list, operator.add]"]}, {"cell_type": "markdown", "id": "9e714ea8-095c-461a-98bc-ee782a84ef5c", "metadata": {}, "source": ["You can try different web search tools. [<PERSON><PERSON>](https://tavily.com/) is one nice option to consider, but ensure your `TAVILY_API_KEY` is set."]}, {"cell_type": "code", "execution_count": 15, "id": "c8bb519a-d08a-4ec7-8f0b-2ce6a9bf7342", "metadata": {}, "outputs": [], "source": ["import os, getpass\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "code", "execution_count": 16, "id": "bfb4f56c-3334-4927-8ed8-62fd384ee43e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from langchain_core.messages import HumanMessage, SystemMessage\n", "\n", "from langchain_community.document_loaders import WikipediaLoader\n", "from langchain_community.tools import TavilySearchResults\n", "\n", "def search_web(state):\n", "    \n", "    \"\"\" Retrieve docs from web search \"\"\"\n", "\n", "    # Search\n", "    tavily_search = TavilySearchResults(max_results=3)\n", "    search_docs = tavily_search.invoke(state['question'])\n", "\n", "     # Format\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document href=\"{doc[\"url\"]}\">\\n{doc[\"content\"]}\\n</Document>'\n", "            for doc in search_docs\n", "        ]\n", "    )\n", "\n", "    return {\"context\": [formatted_search_docs]} \n", "\n", "def search_wikipedia(state):\n", "    \n", "    \"\"\" Retrieve docs from wikipedia \"\"\"\n", "\n", "    # Search\n", "    search_docs = WikipediaLoader(query=state['question'], \n", "                                  load_max_docs=2).load()\n", "\n", "     # Format\n", "    formatted_search_docs = \"\\n\\n---\\n\\n\".join(\n", "        [\n", "            f'<Document source=\"{doc.metadata[\"source\"]}\" page=\"{doc.metadata.get(\"page\", \"\")}\">\\n{doc.page_content}\\n</Document>'\n", "            for doc in search_docs\n", "        ]\n", "    )\n", "\n", "    return {\"context\": [formatted_search_docs]} \n", "\n", "def generate_answer(state):\n", "    \n", "    \"\"\" Node to answer a question \"\"\"\n", "\n", "    # Get state\n", "    context = state[\"context\"]\n", "    question = state[\"question\"]\n", "\n", "    # Template\n", "    answer_template = \"\"\"Answer the question {question} using this context: {context}\"\"\"\n", "    answer_instructions = answer_template.format(question=question, \n", "                                                       context=context)    \n", "    \n", "    # Answer\n", "    answer = llm.invoke([SystemMessage(content=answer_instructions)]+[HumanMessage(content=f\"Answer the question.\")])\n", "      \n", "    # Append it to state\n", "    return {\"answer\": answer}\n", "\n", "# Add nodes\n", "builder = StateGraph(State)\n", "\n", "# Initialize each node with node_secret \n", "builder.add_node(\"search_web\",search_web)\n", "builder.add_node(\"search_wikipedia\", search_wikipedia)\n", "builder.add_node(\"generate_answer\", generate_answer)\n", "\n", "# Flow\n", "builder.add_edge(START, \"search_wikipedia\")\n", "builder.add_edge(START, \"search_web\")\n", "builder.add_edge(\"search_wikipedia\", \"generate_answer\")\n", "builder.add_edge(\"search_web\", \"generate_answer\")\n", "builder.add_edge(\"generate_answer\", END)\n", "graph = builder.compile()\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 17, "id": "fa544ca0-10af-491e-ad7a-477d004413eb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/jz/2tg__xkj7fq6_dzmcc85dlvh0000gn/T/ipykernel_5990/3921850799.py:11: LangChainDeprecationWarning: The class `TavilySearchResults` was deprecated in LangChain 0.3.25 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-tavily package and should be used instead. To use it run `pip install -U :class:`~langchain-tavily` and import as `from :class:`~langchain_tavily import TavilySearch``.\n", "  tavily_search = TavilySearchResults(max_results=3)\n"]}, {"data": {"text/plain": ["\"Nvidia's Q2 2024 earnings were strong, showcasing record revenue and a robust performance in its data center division. The company reported revenue of $30.0 billion, which was up 15% from the previous quarter and up 122% from a year ago. GAAP earnings per diluted share were $0.67, up 12% from the previous quarter and up 168% from a year ago, while non-GAAP earnings per diluted share were $0.68, up 11% from the previous quarter and up 152% from a year ago. Despite a slight dip in gross margin from 78% in Q1 to 75% in Q2, Nvidia remains a dominant force in the AI chip sector. Additionally, Nvidia returned $15.4 billion to shareholders through share repurchases and cash dividends and approved an additional $50.0 billion in share repurchase authorization.\""]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["result = graph.invoke({\"question\": \"How were Nvidia's Q2 2024 earnings\"})\n", "result['answer'].content"]}, {"attachments": {}, "cell_type": "markdown", "id": "3dbbecab-80eb-4f0c-b43a-45542fc0ae9c", "metadata": {}, "source": ["## Using with LangGraph API\n", "\n", "**⚠️ DISCLAIMER**\n", "\n", "Since the filming of these videos, we've updated Studio so that it can be run locally and opened in your browser. This is now the preferred way to run Studio (rather than using the Desktop App as shown in the video). See documentation [here](https://langchain-ai.github.io/langgraph/concepts/langgraph_studio/#local-development-server) on the local development server and [here](https://langchain-ai.github.io/langgraph/how-tos/local-studio/#run-the-development-server). To start the local development server, run the following command in your terminal in the `/studio` directory in this module:\n", "\n", "```\n", "langgraph dev\n", "```\n", "\n", "You should see the following output:\n", "```\n", "- 🚀 API: http://127.0.0.1:2024\n", "- 🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\n", "- 📚 API Docs: http://127.0.0.1:2024/docs\n", "```\n", "\n", "Open your browser and navigate to the Studio UI: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`."]}, {"cell_type": "code", "execution_count": 18, "id": "4bc8ad8d-1365-4801-a8a5-b85cd4965119", "metadata": {}, "outputs": [], "source": ["if 'google.colab' in str(get_ipython()):\n", "    raise Exception(\"Unfortunately LangGraph Studio is currently not supported on Google Colab\")"]}, {"cell_type": "code", "execution_count": 19, "id": "23919dc9-27d8-4d10-b91d-24acdf8c0fb9", "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "client = get_client(url=\"http://127.0.0.1:2024\")"]}, {"cell_type": "code", "execution_count": 20, "id": "ff35e68f-4017-4f45-93cf-ddbb355a0bc1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nvidia's Q2 2024 earnings were strong, showcasing record revenue and a robust performance in its data center division. The company reported revenue of $30.0 billion, which was up 15% from the previous quarter and up 122% from a year ago. GAAP earnings per diluted share were $0.67, up 12% from the previous quarter and up 168% from a year ago. Non-GAAP earnings per diluted share were $0.68, up 11% from the previous quarter and up 152% from a year ago. However, the gross margin saw a decline from 78% in Q1 to 75% in Q2, which could indicate higher production costs or more aggressive pricing strategies. Despite this, Nvidia remains a dominant force in the AI chip sector.\n"]}], "source": ["thread = await client.threads.create()\n", "input_question = {\"question\": \"How were Nvidia Q2 2024 earnings?\"}\n", "async for event in client.runs.stream(thread[\"thread_id\"], \n", "                                      assistant_id=\"parallelization\", \n", "                                      input=input_question, \n", "                                      stream_mode=\"values\"):\n", "    # Check if answer has been added to state  \n", "    if event.data is not None:\n", "        answer = event.data.get('answer', None)\n", "        if answer:\n", "            print(answer['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "54da0234-bddb-4c5b-9b93-b75b7c824d3b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}